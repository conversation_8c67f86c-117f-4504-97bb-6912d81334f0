// Form1.cs - 完整正确的RMG字段映射方案
// 确保所有方法都正确定义，没有调用未定义的方法

using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace FlowCheck_DPLNG
{
    public partial class Form1 : Form
    {
        // ==================== 成员变量 ====================
        
        // 字段映射相关成员变量
        private string currentDeviceManufacturer = "";
        private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
        private bool isRMGDevice = false;

        // 原有的其他成员变量...
        private string stationTableName;
        private string streamFtTableName;
        private string streamTablePrefix;
        private List<Dictionary<string, string>> _reportData;
        // ...

        // ==================== 字段映射核心方法 ====================

        /// <summary>
        /// 初始化字段映射字典
        /// </summary>
        private void InitializeFieldMappings()
        {
            fieldMappings.Clear();
            
            if (isRMGDevice)
            {
                // RMG设备字段映射规则 - 只映射实际存在的4个标准字段
                fieldMappings["FlowVelA"] = "P1Velocity";
                fieldMappings["FlowVelB"] = "P2Velocity";
                fieldMappings["FlowVelC"] = "P3Velocity";
                fieldMappings["FlowVelD"] = "P4Velocity";
                
                // 其他字段映射（根据实际RMG字段结构调整）
                fieldMappings["SndVelA"] = "P1SoundVel";
                fieldMappings["SndVelB"] = "P2SoundVel";
                fieldMappings["SndVelC"] = "P3SoundVel";
                fieldMappings["SndVelD"] = "P4SoundVel";
                
                fieldMappings["StatusA"] = "P1Status";
                fieldMappings["StatusB"] = "P2Status";
                fieldMappings["StatusC"] = "P3Status";
                fieldMappings["StatusD"] = "P4Status";
                
                // 可以根据需要添加更多映射规则，例如：
                // fieldMappings["PctGoodA1"] = "P1PctGood1";
                // fieldMappings["PctGoodA2"] = "P1PctGood2";
                // 等等...
            }
            // 非RMG设备时，fieldMappings为空，使用原始字段名
        }

        /// <summary>
        /// 获取映射后的字段名（之前遗漏的方法）
        /// </summary>
        /// <param name="originalFieldName">原始字段名</param>
        /// <returns>映射后的字段名</returns>
        private string GetMappedFieldName(string originalFieldName)
        {
            if (fieldMappings.ContainsKey(originalFieldName))
            {
                return fieldMappings[originalFieldName];
            }
            return originalFieldName; // 未映射的字段保持原名
        }

        /// <summary>
        /// 安全的字段访问方法 - 支持字段映射和向后兼容
        /// </summary>
        /// <param name="dataRow">数据行</param>
        /// <param name="streamTablePrefix">表前缀</param>
        /// <param name="fieldName">字段名</param>
        /// <returns>字段值</returns>
        private double GetMappedFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string fieldName)
        {
            // 获取映射后的字段名
            string mappedFieldName = GetMappedFieldName(fieldName);
            string fullFieldName = streamTablePrefix + mappedFieldName;
            
            // 尝试访问映射后的字段
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }
            
            // 向后兼容：如果映射字段不存在，尝试原字段名
            string originalFullFieldName = streamTablePrefix + fieldName;
            if (dataRow.ContainsKey(originalFullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[originalFullFieldName]);
            }
            
            // 都不存在时记录警告并返回默认值
            Log.Warning($"字段不存在: {fullFieldName} 或 {originalFullFieldName}");
            return 0.0;
        }

        /// <summary>
        /// RMG特有字段直接访问方法
        /// </summary>
        /// <param name="dataRow">数据行</param>
        /// <param name="streamTablePrefix">表前缀</param>
        /// <param name="rmgFieldName">RMG字段名（如P5Velocity）</param>
        /// <returns>字段值</returns>
        private double GetRMGSpecificFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string rmgFieldName)
        {
            string fullFieldName = streamTablePrefix + rmgFieldName;
            
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }
            
            Log.Warning($"RMG特有字段不存在: {fullFieldName}");
            return 0.0;
        }

        /// <summary>
        /// 查询设备厂商名的方法
        /// </summary>
        /// <param name="flowMeterTag">流量表标签</param>
        /// <returns>设备厂商名</returns>
        private async Task<string> GetDeviceManufacturerAsync(string flowMeterTag)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(
                        $"SELECT DeviceManufacturer FROM {stationTableName} WHERE FlowMeterTag = @FlowMeterTag", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                        var result = await command.ExecuteScalarAsync();
                        return result?.ToString()?.Trim() ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"获取设备厂商信息失败: {ex.Message}");
                return "";
            }
        }

        // ==================== 修改后的主要方法 ====================

        /// <summary>
        /// 修改后的cmdStart_Click方法
        /// </summary>
        public async void cmdStart_Click(object sender, EventArgs e)
        {
            try
            {
                // 原有的验证逻辑...
                if (cbFlowMeter.SelectedIndex <= 0 || cbFlowComputer.SelectedIndex <= 0)
                {
                    MessageBox.Show("Please select the Meter and Flow Computer.");
                    return;
                }

                cmdStart.Enabled = false;
                streamFtTableName = cbFlowMeter.Text;

                // 新增：查询设备厂商并设置字段映射
                currentDeviceManufacturer = await GetDeviceManufacturerAsync(streamFtTableName);
                isRMGDevice = currentDeviceManufacturer.Equals("RMG", StringComparison.OrdinalIgnoreCase);
                InitializeFieldMappings();

                Log.Info($"设备厂商: {currentDeviceManufacturer}, 是否为RMG设备: {isRMGDevice}");

                // 原有的其他逻辑继续...
                // 这里应该是原有的数据读取和处理逻辑
                // ...

                // 处理报告数据
                ProcessReportData();
            }
            catch (Exception ex)
            {
                Log.Error($"cmdStart_Click error: {ex.Message}");
                MessageBox.Show($"启动失败: {ex.Message}");
            }
            finally
            {
                cmdStart.Enabled = true;
            }
        }

        // ==================== 数据处理方法 ====================

        /// <summary>
        /// 修改后的数据处理方法
        /// </summary>
        private void ProcessReportData()
        {
            if (_reportData == null || _reportData.Count == 0)
            {
                Log.Warning("没有数据需要处理");
                return;
            }

            for (int i = 0; i < _reportData.Count; i++)
            {
                try
                {
                    // 处理标准的4个FlowVel字段（通过映射访问）
                    var flowVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
                    var flowVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelB"), 2);
                    var flowVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelC"), 2);
                    var flowVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelD"), 2);
                    
                    // RMG设备的额外字段（直接访问）
                    double flowVelP5 = 0, flowVelP6 = 0;
                    if (isRMGDevice)
                    {
                        flowVelP5 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P5Velocity"), 2);
                        flowVelP6 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P6Velocity"), 2);
                        
                        Log.Info($"RMG额外字段 - P5Velocity: {flowVelP5}, P6Velocity: {flowVelP6}");
                    }

                    // 处理其他字段（同样通过映射）
                    var sndVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelA"), 2);
                    var sndVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelB"), 2);
                    var sndVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelC"), 2);
                    var sndVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelD"), 2);
                    
                    var statusA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusA"), 2);
                    var statusB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusB"), 2);
                    var statusC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusC"), 2);
                    var statusD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusD"), 2);
                    
                    // 根据设备类型进行不同的数据处理
                    if (isRMGDevice)
                    {
                        ProcessRMGData(flowVelA, flowVelB, flowVelC, flowVelD, flowVelP5, flowVelP6);
                    }
                    else
                    {
                        ProcessStandardData(flowVelA, flowVelB, flowVelC, flowVelD);
                    }

                    // 其他原有的处理逻辑...
                    // 注意：所有原有的字段访问都应该替换为GetMappedFieldValue调用
                    
                    // 例如：
                    // 原来：ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodA1"])
                    // 改为：GetMappedFieldValue(_reportData[i], streamTablePrefix, "PctGoodA1")
                }
                catch (Exception ex)
                {
                    Log.Error($"处理数据行 {i} 时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// RMG设备专用数据处理方法（6路数据）
        /// </summary>
        private void ProcessRMGData(double p1Vel, double p2Vel, double p3Vel, double p4Vel, double p5Vel, double p6Vel)
        {
            Log.Info($"RMG设备数据处理 - P1:{p1Vel}, P2:{p2Vel}, P3:{p3Vel}, P4:{p4Vel}, P5:{p5Vel}, P6:{p6Vel}");
            
            // 计算6路平均值
            double avgVelocity = (p1Vel + p2Vel + p3Vel + p4Vel + p5Vel + p6Vel) / 6.0;
            
            // 其他RMG特有的计算逻辑...
            // 例如：6路数据的特殊验证、报告生成等
        }

        /// <summary>
        /// 标准设备数据处理方法（4路数据）
        /// </summary>
        private void ProcessStandardData(double velA, double velB, double velC, double velD)
        {
            Log.Info($"标准设备数据处理 - A:{velA}, B:{velB}, C:{velC}, D:{velD}");
            
            // 计算4路平均值
            double avgVelocity = (velA + velB + velC + velD) / 4.0;
            
            // 其他标准计算逻辑...
        }

        // ==================== 辅助方法 ====================

        /// <summary>
        /// 获取所有速度数据（根据设备类型返回4路或6路）
        /// </summary>
        private List<double> GetAllVelocityData(Dictionary<string, string> dataRow, string streamTablePrefix)
        {
            var velocities = new List<double>();
            
            if (isRMGDevice)
            {
                // RMG设备：获取6个速度值
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelA")); // → P1Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelB")); // → P2Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelC")); // → P3Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelD")); // → P4Velocity
                velocities.Add(GetRMGSpecificFieldValue(dataRow, streamTablePrefix, "P5Velocity"));
                velocities.Add(GetRMGSpecificFieldValue(dataRow, streamTablePrefix, "P6Velocity"));
            }
            else
            {
                // 标准设备：获取4个速度值
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelA"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelB"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelC"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelD"));
            }
            
            return velocities;
        }

        /// <summary>
        /// 动态生成报告列标题（根据设备类型）
        /// </summary>
        private List<string> GetVelocityColumnHeaders()
        {
            if (isRMGDevice)
            {
                return new List<string> { "P1Velocity", "P2Velocity", "P3Velocity", "P4Velocity", "P5Velocity", "P6Velocity" };
            }
            else
            {
                return new List<string> { "FlowVelA", "FlowVelB", "FlowVelC", "FlowVelD" };
            }
        }

        // ==================== 其他原有方法保持不变 ====================
        
        // 注意：原有的ParseDoubleOrDefault、Log等方法保持不变
        // 只需要将所有字段访问替换为GetMappedFieldValue调用即可
    }
}

/*
实施检查清单：

✅ 1. 定义了所有必要的成员变量
✅ 2. 定义了GetMappedFieldName方法（之前遗漏）
✅ 3. 定义了GetMappedFieldValue方法
✅ 4. 定义了GetRMGSpecificFieldValue方法
✅ 5. 定义了InitializeFieldMappings方法
✅ 6. 定义了GetDeviceManufacturerAsync方法
✅ 7. 提供了完整的使用示例
✅ 8. 没有调用未定义的方法
✅ 9. 包含错误处理和日志记录
✅ 10. 支持向后兼容

接下来需要做的：
1. 将所有原有的 _reportData[i][streamTablePrefix + "字段名"] 替换为 GetMappedFieldValue(_reportData[i], streamTablePrefix, "字段名")
2. 根据实际的RMG字段结构调整fieldMappings字典
3. 测试验证功能正确性
*/ 