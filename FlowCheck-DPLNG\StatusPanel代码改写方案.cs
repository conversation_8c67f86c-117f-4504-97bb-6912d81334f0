// StatusPanel背景色设置代码的改写方案

// 方案1：如果resultFT没有表前缀，创建简化的字段访问方法
/// <summary>
/// 简化的字段访问方法（适用于没有表前缀的数据字典）
/// </summary>
/// <param name="dataDict">数据字典</param>
/// <param name="fieldName">字段名</param>
/// <returns>字段值</returns>
private double GetMappedFieldValueSimple(Dictionary<string, string> dataDict, string fieldName)
{
    // 获取映射后的字段名
    string mappedFieldName = GetMappedFieldName(fieldName);
    
    // 尝试访问映射后的字段
    if (dataDict.ContainsKey(mappedFieldName))
    {
        return ParseDoubleOrDefault(dataDict[mappedFieldName]);
    }
    
    // 向后兼容：如果映射字段不存在，尝试原字段名
    if (dataDict.ContainsKey(fieldName))
    {
        return ParseDoubleOrDefault(dataDict[fieldName]);
    }
    
    // 都不存在时记录警告并返回默认值
    Log.Warning($"字段不存在: {mappedFieldName} 或 {fieldName}");
    return 0.0;
}

// 原始代码：
/*
panelStatusA.BackColor = ParseDoubleOrDefault(resultFT["StatusA"]) == 0
    ? Color.Green
    : Color.Red;
panelStatusB.BackColor = ParseDoubleOrDefault(resultFT["StatusB"]) == 0
    ? Color.Green
    : Color.Red;
panelStatusC.BackColor = ParseDoubleOrDefault(resultFT["StatusC"]) == 0
    ? Color.Green
    : Color.Red;
panelStatusD.BackColor = ParseDoubleOrDefault(resultFT["StatusD"]) == 0
    ? Color.Green
    : Color.Red;
panelSystemStatus.BackColor = ParseDoubleOrDefault(resultFT["SystemStatus"]) == 0
    ? Color.Green
    : Color.Red;
*/

// 改写后的代码（方案1 - 使用简化方法）：
panelStatusA.BackColor = GetMappedFieldValueSimple(resultFT, "StatusA") == 0
    ? Color.Green
    : Color.Red;
panelStatusB.BackColor = GetMappedFieldValueSimple(resultFT, "StatusB") == 0
    ? Color.Green
    : Color.Red;
panelStatusC.BackColor = GetMappedFieldValueSimple(resultFT, "StatusC") == 0
    ? Color.Green
    : Color.Red;
panelStatusD.BackColor = GetMappedFieldValueSimple(resultFT, "StatusD") == 0
    ? Color.Green
    : Color.Red;
panelSystemStatus.BackColor = GetMappedFieldValueSimple(resultFT, "SystemStatus") == 0
    ? Color.Green
    : Color.Red;

// ===================================================================

// 方案2：如果resultFT实际上应该有表前缀，使用完整的方法
// 改写后的代码（方案2 - 使用完整方法）：
panelStatusA.BackColor = GetMappedFieldValue(resultFT, streamTablePrefix, "StatusA") == 0
    ? Color.Green
    : Color.Red;
panelStatusB.BackColor = GetMappedFieldValue(resultFT, streamTablePrefix, "StatusB") == 0
    ? Color.Green
    : Color.Red;
panelStatusC.BackColor = GetMappedFieldValue(resultFT, streamTablePrefix, "StatusC") == 0
    ? Color.Green
    : Color.Red;
panelStatusD.BackColor = GetMappedFieldValue(resultFT, streamTablePrefix, "StatusD") == 0
    ? Color.Green
    : Color.Red;
panelSystemStatus.BackColor = GetMappedFieldValue(resultFT, streamTablePrefix, "SystemStatus") == 0
    ? Color.Green
    : Color.Red;

// ===================================================================

// 方案3：更进一步优化 - 创建专门的状态面板更新方法
/// <summary>
/// 更新状态面板背景色
/// </summary>
/// <param name="resultFT">数据字典</param>
private void UpdateStatusPanels(Dictionary<string, string> resultFT)
{
    // 获取状态值
    var statusA = GetMappedFieldValueSimple(resultFT, "StatusA");
    var statusB = GetMappedFieldValueSimple(resultFT, "StatusB");
    var statusC = GetMappedFieldValueSimple(resultFT, "StatusC");
    var statusD = GetMappedFieldValueSimple(resultFT, "StatusD");
    var systemStatus = GetMappedFieldValueSimple(resultFT, "SystemStatus");
    
    // 设置面板颜色
    panelStatusA.BackColor = statusA == 0 ? Color.Green : Color.Red;
    panelStatusB.BackColor = statusB == 0 ? Color.Green : Color.Red;
    panelStatusC.BackColor = statusC == 0 ? Color.Green : Color.Red;
    panelStatusD.BackColor = statusD == 0 ? Color.Green : Color.Red;
    panelSystemStatus.BackColor = systemStatus == 0 ? Color.Green : Color.Red;
    
    // 记录状态信息（可选）
    Log.Info($"状态更新 - A:{statusA}, B:{statusB}, C:{statusC}, D:{statusD}, System:{systemStatus}");
}

// 使用优化后的方法：
// UpdateStatusPanels(resultFT);

// ===================================================================

// 方案4：RMG设备专用处理 - 如果需要显示6个状态面板
/// <summary>
/// 更新状态面板背景色（支持RMG设备6路状态）
/// </summary>
/// <param name="resultFT">数据字典</param>
private void UpdateStatusPanelsWithRMGSupport(Dictionary<string, string> resultFT)
{
    if (isRMGDevice)
    {
        // RMG设备：显示6个状态（P1-P6）
        var statusP1 = GetMappedFieldValueSimple(resultFT, "StatusA"); // 映射为P1Status
        var statusP2 = GetMappedFieldValueSimple(resultFT, "StatusB"); // 映射为P2Status
        var statusP3 = GetMappedFieldValueSimple(resultFT, "StatusC"); // 映射为P3Status
        var statusP4 = GetMappedFieldValueSimple(resultFT, "StatusD"); // 映射为P4Status
        
        // RMG特有的P5和P6状态（如果存在对应面板）
        var statusP5 = GetRMGSpecificFieldValueSimple(resultFT, "P5Status");
        var statusP6 = GetRMGSpecificFieldValueSimple(resultFT, "P6Status");
        
        // 设置面板颜色（假设有对应的面板控件）
        panelStatusA.BackColor = statusP1 == 0 ? Color.Green : Color.Red;
        panelStatusB.BackColor = statusP2 == 0 ? Color.Green : Color.Red;
        panelStatusC.BackColor = statusP3 == 0 ? Color.Green : Color.Red;
        panelStatusD.BackColor = statusP4 == 0 ? Color.Green : Color.Red;
        
        // 如果有P5和P6面板（需要在界面上添加）
        // panelStatusP5.BackColor = statusP5 == 0 ? Color.Green : Color.Red;
        // panelStatusP6.BackColor = statusP6 == 0 ? Color.Green : Color.Red;
        
        Log.Info($"RMG状态更新 - P1:{statusP1}, P2:{statusP2}, P3:{statusP3}, P4:{statusP4}, P5:{statusP5}, P6:{statusP6}");
    }
    else
    {
        // 标准设备：显示4个状态
        UpdateStatusPanels(resultFT);
    }
}

// 需要添加的辅助方法：
/// <summary>
/// RMG特有字段简化访问方法
/// </summary>
/// <param name="dataDict">数据字典</param>
/// <param name="rmgFieldName">RMG字段名</param>
/// <returns>字段值</returns>
private double GetRMGSpecificFieldValueSimple(Dictionary<string, string> dataDict, string rmgFieldName)
{
    if (dataDict.ContainsKey(rmgFieldName))
    {
        return ParseDoubleOrDefault(dataDict[rmgFieldName]);
    }
    
    Log.Warning($"RMG特有字段不存在: {rmgFieldName}");
    return 0.0;
}

// 推荐使用方案：
// 1. 如果resultFT确实没有表前缀，使用方案1或方案3
// 2. 如果resultFT应该有表前缀，使用方案2
// 3. 如果需要支持RMG设备的6路状态显示，使用方案4 