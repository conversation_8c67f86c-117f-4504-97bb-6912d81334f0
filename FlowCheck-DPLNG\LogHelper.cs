﻿using System;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace FlowCheck_DPLNG
{
    public static class LogHelper
    {
        public static void LogError(
            Exception ex,
            string customMessage,
            // 下面这三个参数是C#的“魔法”，你不需要手动传值，编译器会自动填充
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            string errorMessage = $@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}]
        错误位置: {Path.GetFileName(sourceFilePath)} (行: {sourceLineNumber})
        方法/属性: {memberName}
        自定义信息: {customMessage}
        --- 技术细节 ---
        {ex.ToString()}
        -------------------------------------------------
        ";

            // 同样，写入文件或显示弹窗
            string logPath = Path.Combine(Path.GetDirectoryName(Application.ExecutablePath), "error_log.txt");
            File.AppendAllText(logPath, errorMessage);
        }
    }
}