# FlowCheck-DPLNG 项目技术分析报告

## 项目概述

FlowCheck-DPLNG是一个专业的天然气流量计检测系统，专为大鹏LNG项目开发。系统主要功能包括流量计数据稳定性检查、声速(SOS)计算、实时监控和报表生成，支持30多个天然气站点的监控管理。

## 技术架构

### 开发平台
- **.NET Framework 4.8** + **WinForms**
- **C# 语言**，支持最新语法特性
- **x86平台**，单实例运行模式
- **Visual Studio 2012+** 项目格式

### 核心依赖库
| 库名称 | 版本 | 用途 |
|--------|------|------|
| EPPlus | 5.7.4 | Excel报表生成 |
| ScottPlot | 4.1.74 | 数据可视化图表 |
| log4net | 2.0.17 | 日志记录系统 |
| HslCommunication | 12.1.3 | 工业通信协议 |
| Newtonsoft.Json | 13.0.1 | JSON配置解析 |
| SkiaSharp | 3.118.0 | 图形渲染支持 |

### 数据库架构
- **数据库**：SQL Server (CBM_UKMS_IOServer)
- **核心表**：Selection_Table (140条站点配置记录)
- **动态表**：每个流量计对应独立的历史数据表
- **连接方式**：SQL Server身份验证

## 核心业务模块

### 1. 数据管理层

#### StationDataManager
```csharp
- GetAllStationsAsync(): 异步获取所有站点
- GetFlowMeterTagsByStationAsync(): 获取站点下的流量计
- GetFlowComputerTagsByFlowMeterAsync(): 获取流量计算机信息
- GetDeviceManufacturerAsync(): 获取设备厂商信息
```

#### StationConfigManager
```csharp
- LoadConfig(): 加载stations.json配置
- GetStationByName(): 根据名称获取站点信息
- GetStreamsByStation(): 获取站点的流量计和流量计算机列表
```

#### XmlConfigReader
```csharp
- GetConnectionString(): 构建数据库连接字符串
- GetReportPath(): 获取报表输出路径
- GetStationDataTableName(): 获取站点数据表名
```

### 2. 数据处理层

#### DataProcessor
```csharp
- ReadDataInTimeRangeAsync(): 读取指定时间范围的历史数据
- CheckDataStabilityAsync(): 异步数据稳定性检查
- 支持10分钟历史数据批量处理
```

#### DataAnalyzer
```csharp
- AnalyzeData(): 数据稳定性分析
- InitializeStabilityLimits(): 初始化稳定性限值
- CalculateStandardDeviation(): 计算标准差
```

#### StabilityThresholdReader
```csharp
- ReadThresholds(): 从XML读取稳定性阈值配置
- RangeThresholds: 范围阈值字典
- StdDevThresholds: 标准差阈值字典
```

### 3. 算法计算层

#### AGA10算法集成
```csharp
[DllImport("Lib\\234dll.dll")]
- AGA10_Init(): 初始化AGA10算法
- Crit(): 执行临界流计算
- AGA10_UnInit(): 清理算法资源
```

#### 支持的气体组分
- 甲烷(METHANE)、乙烷(ETHANE)、丙烷(PROPANE)
- 正丁烷(N_BUTANE)、异丁烷(I_BUTANE)
- 正戊烷(N_PENTANE)、异戊烷(I_PENTANE)、新戊烷(NEO_PENTANE)
- 己烷(HEXANE)、氦气(HELIUM)、水(H2O)
- 硫化氢(H2S)、氮气(NITROGEN)、C6+(C6PLUS)
- 二氧化碳(CO2)、氢气(HYDROGEN)

### 4. 可视化层

#### DataPlotter
```csharp
- PlotData(): 绘制多字段数据图表
- ConfigurePlot(): 配置图表样式和坐标轴
- SetupTooltip(): 设置鼠标悬停提示
- 支持实时数据可视化和交互
```

#### 实时监控面板
- **绿色**：数据稳定，系统正常
- **红色**：数据不稳定或系统异常
- **黄色**：系统状态警告
- **灰色**：未检测或初始状态

## 关键技术特性

### 1. 设备兼容性

#### 标准设备支持
- 4路超声波流量计
- 标准字段命名：FlowVelA/B/C/D

#### RMG设备支持
- 6路超声波流量计
- RMG字段映射：P1Velocity~P6Velocity
- 动态字段映射机制

```csharp
// 字段映射示例
FlowVelA → P1Velocity (RMG设备)
FlowVelB → P2Velocity (RMG设备)
FlowVelC → P3Velocity (RMG设备)
FlowVelD → P4Velocity (RMG设备)
```

### 2. 数据稳定性检查标准

| 参数类型 | 范围阈值 | 标准差阈值 | 说明 |
|----------|----------|------------|------|
| 气体组分(SLCT_*) | 0.1 | 0.03 | 各种气体组分含量 |
| 压力(PressInuse) | 500 | 50 | 使用压力 |
| 温度(TempInuse) | 0.5 | 0.1 | 使用温度 |
| 声速(USMAvgVOS) | 0.5 | 0.15 | 超声波平均声速 |

### 3. 报表生成系统

#### 双模板支持
- **标准模板**：SOSCheckReportTemplate.xlsx
- **RMG模板**：SOSCheckReportTemplate_RMG.xlsx

#### 报表特性
- 自动命名：GUSM_SOSCHECK_{设备}_{时间}.xlsx
- 数据保护：Excel工作表密码保护(Admin123)
- 多工作表：包含配置、数据、图表等多个工作表

### 4. 日志系统

#### log4net配置
```xml
- 按日期滚动：yyyy-MM-dd.log
- 日志级别：Debug/Info/Warn/Error/Fatal
- 输出格式：时间 [线程] 级别 记录器 - 消息
```

#### LogHelper增强
```csharp
- 自动获取调用者信息：文件名、行号、方法名
- 详细错误追踪：异常堆栈、自定义消息
- 错误日志文件：error_log.txt
```

## 业务流程

### 1. 系统启动流程
```
Program.cs 
  ↓
单实例检查 
  ↓
log4net配置初始化 
  ↓
Form1主窗体加载 
  ↓
配置文件读取 
  ↓
数据库连接建立
```

### 2. 数据检查流程
```
选择站点 
  ↓
选择流量计 
  ↓
设备厂商识别 
  ↓
字段映射初始化 
  ↓
读取10分钟历史数据 
  ↓
并行稳定性分析 
  ↓
AGA10算法计算SOS 
  ↓
生成Excel报表
```

### 3. 实时监控流程
```
DatabasePoller启动 
  ↓
定时数据库查询 
  ↓
数据变化检测 
  ↓
UI状态更新 
  ↓
异常处理和日志记录
```

## 配置文件详解

### 1. AppConfig.xml
```xml
<Configuration>
  <DatabaseConnection>
    <Server>.</Server>
    <Database>CBM_UKMS_IOServer</Database>
    <Username>sa</Username>
    <Password>Welcome1</Password>
  </DatabaseConnection>
  <ReportSettings>
    <ReportPath>C:\Reports\</ReportPath>
  </ReportSettings>
  <StationDataTable>
    <TableName>Selection_Table</TableName>
  </StationDataTable>
</Configuration>
```

### 2. stations.json
```json
{
  "stations": [
    {
      "name": "Trunkline",
      "streams": ["FT-11642A", "FT-11642B"],
      "fc": ["FC-11642A", "FC-11642B"]
    }
    // ... 30+ 站点配置
  ]
}
```

### 3. StabilityThresholds.xml
```xml
<StabilityThresholds>
  <Field name="SLCT">
    <RangeThreshold>0.1</RangeThreshold>
    <StdDevThreshold>0.03</StdDevThreshold>
  </Field>
  // ... 其他参数配置
</StabilityThresholds>
```

## 错误处理机制

### 1. 自定义异常
```csharp
public class MethodException : Exception
{
    public string MethodName { get; }
    // 提供方法级别的错误追踪
}
```

### 2. 错误处理策略
- **数据库错误**：连接重试、事务回滚
- **文件操作错误**：路径检查、权限验证
- **算法计算错误**：参数验证、结果检查
- **UI操作错误**：用户友好提示、状态恢复

### 3. 日志记录
- **详细追踪**：文件名、行号、方法名自动记录
- **分级记录**：不同级别的日志分别处理
- **用户反馈**：错误信息本地化显示

## 性能优化

### 1. 异步编程
- 数据库操作全面异步化
- UI更新与数据处理分离
- 避免界面冻结

### 2. 资源管理
- IDisposable模式实现
- 数据库连接及时释放
- 内存泄漏防护

### 3. 并发控制
- 数据库轮询防重叠机制
- 单实例运行保护
- 线程安全的UI更新

## 部署和维护

### 1. 部署要求
- Windows操作系统
- .NET Framework 4.8运行时
- SQL Server数据库访问权限
- Excel软件(用于报表查看)

### 2. 配置文件位置
- Config/AppConfig.xml：数据库配置
- Config/stations.json：站点配置
- Config/StabilityThresholds.xml：阈值配置
- log4net.config：日志配置

### 3. 日志文件位置
- logs/：log4net日志文件
- error_log.txt：详细错误日志
- C:\FlowCheckLog\：备用日志目录

## 总结

FlowCheck-DPLNG是一个技术成熟、功能完善的工业级应用系统。它具有：

- **专业性**：针对天然气流量计检测的专业算法和标准
- **可靠性**：完善的错误处理和日志记录机制
- **扩展性**：支持多种设备类型和灵活的配置管理
- **易用性**：直观的用户界面和友好的操作体验
- **维护性**：清晰的代码结构和详细的技术文档

该系统在大鹏LNG项目中发挥着重要作用，为天然气流量计的准确性和可靠性提供了有力保障。
