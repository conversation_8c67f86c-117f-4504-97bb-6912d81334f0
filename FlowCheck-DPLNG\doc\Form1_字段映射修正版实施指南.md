# FlowCheck-DPLNG RMG设备字段映射修正版实施指南

## 问题回顾与修正

**原始问题：**
- 原表没有`FlowVelE`和`FlowVelF`字段
- RMG表有`P5Velocity`和`P6Velocity`字段
- 需要在RMG程序中使用这6个字段（P1-P6Velocity）

**修正后的解决方案：**
1. **双重访问机制**：映射访问 + 直接访问
2. **4路映射**：FlowVelA/B/C/D → P1/P2/P3/P4Velocity  
3. **2路直接访问**：直接读取P5Velocity和P6Velocity
4. **动态处理**：根据设备类型处理4路或6路数据

## 修正后的实施步骤

### Step 1: 添加成员变量（不变）
```csharp
private string currentDeviceManufacturer = "";
private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
private bool isRMGDevice = false;
```

### Step 2: 修正后的字段映射初始化
```csharp
private void InitializeFieldMappings()
{
    fieldMappings.Clear();
    
    if (isRMGDevice)
    {
        // 只映射实际存在的4个标准字段
        fieldMappings["FlowVelA"] = "P1Velocity";
        fieldMappings["FlowVelB"] = "P2Velocity";
        fieldMappings["FlowVelC"] = "P3Velocity";
        fieldMappings["FlowVelD"] = "P4Velocity";
        
        // 其他字段映射（根据实际需要添加）
        fieldMappings["SndVelA"] = "P1SoundVel";
        fieldMappings["SndVelB"] = "P2SoundVel";
        fieldMappings["SndVelC"] = "P3SoundVel";
        fieldMappings["SndVelD"] = "P4SoundVel";
        
        // 注意：不再映射FlowVelE/F，因为它们不存在
    }
}
```

### Step 3: 添加RMG特有字段直接访问方法
```csharp
// 新增方法：直接访问RMG特有字段
private double GetRMGSpecificFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string rmgFieldName)
{
    string fullFieldName = streamTablePrefix + rmgFieldName;
    
    if (dataRow.ContainsKey(fullFieldName))
    {
        return ParseDoubleOrDefault(dataRow[fullFieldName]);
    }
    
    Log.Warning($"RMG特有字段不存在: {fullFieldName}");
    return 0.0;
}
```

### Step 4: 修改数据处理逻辑
需要在数据处理的地方修改为双重访问模式：

**原始代码修改示例：**
```csharp
// 原来的代码处理4路数据
var flowVelA = Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelA"]), 2);
var flowVelB = Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelB"]), 2);
var flowVelC = Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelC"]), 2);
var flowVelD = Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelD"]), 2);

// 修改后的代码 - 支持4路或6路数据
var flowVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
var flowVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelB"), 2);
var flowVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelC"), 2);
var flowVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelD"), 2);

// RMG设备的额外字段
double flowVelP5 = 0, flowVelP6 = 0;
if (isRMGDevice)
{
    flowVelP5 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P5Velocity"), 2);
    flowVelP6 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P6Velocity"), 2);
}
```

### Step 5: 添加设备类型特定的处理方法
```csharp
// RMG设备数据处理（6路）
private void ProcessRMGData(double p1, double p2, double p3, double p4, double p5, double p6)
{
    // 6路数据的特殊处理逻辑
    double avgVelocity = (p1 + p2 + p3 + p4 + p5 + p6) / 6.0;
    // 其他RMG特有计算...
}

// 标准设备数据处理（4路）
private void ProcessStandardData(double a, double b, double c, double d)
{
    // 4路数据的标准处理逻辑
    double avgVelocity = (a + b + c + d) / 4.0;
    // 其他标准计算...
}
```

## 关键修改点定位

### 在Form1.cs中需要修改的具体位置：

1. **约第999-1005行的FlowVel处理：**
```csharp
// 找到这些行：
Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelA"]), 2);
Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelB"]), 2);
Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelC"]), 2);
Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelD"]), 2);

// 修改为：
var flowVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
var flowVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelB"), 2);
var flowVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelC"), 2);
var flowVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelD"), 2);

// 然后添加RMG特有字段处理：
double flowVelP5 = 0, flowVelP6 = 0;
if (isRMGDevice)
{
    flowVelP5 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P5Velocity"), 2);
    flowVelP6 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P6Velocity"), 2);
}
```

2. **报告生成部分（如果有）需要动态调整列数：**
```csharp
// 动态确定速度字段数量
List<double> velocities = GetAllVelocityData(_reportData[i], streamTablePrefix);
List<string> headers = GetVelocityColumnHeaders();

// 这样可以自动适应4路或6路数据
```

## 数据流程图

```
用户选择设备 → cmdStart_Click
    ↓
查询DeviceManufacturer
    ↓
设置isRMGDevice标志
    ↓
InitializeFieldMappings() - 设置A/B/C/D映射
    ↓
读取数据到_reportData
    ↓
数据处理：
├─ 标准字段：通过GetMappedFieldValue访问（A→P1, B→P2, C→P3, D→P4）
└─ RMG特有：通过GetRMGSpecificFieldValue直接访问（P5, P6）
    ↓
根据isRMGDevice选择：
├─ ProcessRMGData(6个参数)
└─ ProcessStandardData(4个参数)
```

## 测试验证策略

### 1. 标准设备测试
- DeviceManufacturer != "RMG"
- 确认只访问FlowVelA/B/C/D
- 验证4路数据处理正确

### 2. RMG设备测试
- DeviceManufacturer = "RMG"
- 确认FlowVelA映射为P1Velocity等
- 验证P5Velocity和P6Velocity能正确读取
- 确认6路数据处理正确

### 3. 错误情况测试
- RMG表中缺少P5或P6字段
- 非RMG设备误设为RMG
- 数据库连接异常等

## 关键优势

1. **完全兼容**：标准设备功能不受任何影响
2. **灵活扩展**：RMG设备获得完整的6路数据支持
3. **错误安全**：字段不存在时优雅降级
4. **性能优秀**：最小化额外开销
5. **易于维护**：清晰的代码结构和错误处理

这个修正版本解决了P5Velocity/P6Velocity字段访问的问题，确保RMG设备能够正确使用所有6个速度字段。 