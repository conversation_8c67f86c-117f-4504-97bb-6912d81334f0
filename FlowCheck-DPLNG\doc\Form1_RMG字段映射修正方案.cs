using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace FlowCheck_DPLNG
{
    public partial class Form1 : Form
    {
        // 字段映射相关成员变量
        private string currentDeviceManufacturer = "";
        private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
        private bool isRMGDevice = false;

        // 修正后的字段映射初始化方法
        private void InitializeFieldMappings()
        {
            fieldMappings.Clear();
            
            if (isRMGDevice)
            {
                // RMG设备字段映射规则 - 只映射实际存在的字段
                fieldMappings["FlowVelA"] = "P1Velocity";
                fieldMappings["FlowVelB"] = "P2Velocity";
                fieldMappings["FlowVelC"] = "P3Velocity";
                fieldMappings["FlowVelD"] = "P4Velocity";
                
                // 其他字段映射（根据实际RMG字段结构调整）
                fieldMappings["SndVelA"] = "P1SoundVel";
                fieldMappings["SndVelB"] = "P2SoundVel";
                fieldMappings["SndVelC"] = "P3SoundVel";
                fieldMappings["SndVelD"] = "P4SoundVel";
                
                fieldMappings["StatusA"] = "P1Status";
                fieldMappings["StatusB"] = "P2Status";
                fieldMappings["StatusC"] = "P3Status";
                fieldMappings["StatusD"] = "P4Status";
                
                // 根据实际需要添加更多映射规则
            }
        }

        // 修正后的字段访问方法 - 支持RMG特有字段直接访问
        private double GetMappedFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string fieldName)
        {
            string mappedFieldName = GetMappedFieldName(fieldName);
            string fullFieldName = streamTablePrefix + mappedFieldName;
            
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }
            
            // 向后兼容：如果映射字段不存在，尝试原字段名
            string originalFullFieldName = streamTablePrefix + fieldName;
            if (dataRow.ContainsKey(originalFullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[originalFullFieldName]);
            }
            
            Log.Warning($"字段不存在: {fullFieldName} 或 {originalFullFieldName}");
            return 0.0;
        }

        // 新增：RMG特有字段直接访问方法
        private double GetRMGSpecificFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string rmgFieldName)
        {
            string fullFieldName = streamTablePrefix + rmgFieldName;
            
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }
            
            Log.Warning($"RMG特有字段不存在: {fullFieldName}");
            return 0.0;
        }

        // 修改后的数据处理代码示例
        private void ProcessReportData()
        {
            for (int i = 0; i < _reportData.Count; i++)
            {
                // 处理标准的4个FlowVel字段（已映射）
                var flowVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
                var flowVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelB"), 2);
                var flowVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelC"), 2);
                var flowVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelD"), 2);
                
                // RMG设备的额外字段直接访问
                double flowVelP5 = 0, flowVelP6 = 0;
                if (isRMGDevice)
                {
                    flowVelP5 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P5Velocity"), 2);
                    flowVelP6 = Math.Round(GetRMGSpecificFieldValue(_reportData[i], streamTablePrefix, "P6Velocity"), 2);
                    
                    Log.Info($"RMG额外字段 - P5Velocity: {flowVelP5}, P6Velocity: {flowVelP6}");
                }

                // 处理其他字段（已映射）
                var sndVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelA"), 2);
                var statusA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusA"), 2);
                
                // 根据设备类型进行不同的数据处理
                if (isRMGDevice)
                {
                    ProcessRMGData(flowVelA, flowVelB, flowVelC, flowVelD, flowVelP5, flowVelP6);
                }
                else
                {
                    ProcessStandardData(flowVelA, flowVelB, flowVelC, flowVelD);
                }
            }
        }

        // RMG设备专用数据处理方法
        private void ProcessRMGData(double p1Vel, double p2Vel, double p3Vel, double p4Vel, double p5Vel, double p6Vel)
        {
            // RMG设备的6路速度数据处理逻辑
            Log.Info($"RMG设备数据处理 - P1:{p1Vel}, P2:{p2Vel}, P3:{p3Vel}, P4:{p4Vel}, P5:{p5Vel}, P6:{p6Vel}");
            
            // 计算6路平均值
            double avgVelocity = (p1Vel + p2Vel + p3Vel + p4Vel + p5Vel + p6Vel) / 6.0;
            
            // 其他RMG特有的计算逻辑...
        }

        // 标准设备数据处理方法
        private void ProcessStandardData(double velA, double velB, double velC, double velD)
        {
            // 标准4路速度数据处理逻辑
            Log.Info($"标准设备数据处理 - A:{velA}, B:{velB}, C:{velC}, D:{velD}");
            
            // 计算4路平均值
            double avgVelocity = (velA + velB + velC + velD) / 4.0;
            
            // 其他标准计算逻辑...
        }

        // 获取RMG设备的所有速度数据（用于报告等）
        private List<double> GetAllVelocityData(Dictionary<string, string> dataRow, string streamTablePrefix)
        {
            var velocities = new List<double>();
            
            if (isRMGDevice)
            {
                // RMG设备：获取6个速度值
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelA")); // P1Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelB")); // P2Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelC")); // P3Velocity
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelD")); // P4Velocity
                velocities.Add(GetRMGSpecificFieldValue(dataRow, streamTablePrefix, "P5Velocity"));
                velocities.Add(GetRMGSpecificFieldValue(dataRow, streamTablePrefix, "P6Velocity"));
            }
            else
            {
                // 标准设备：获取4个速度值
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelA"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelB"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelC"));
                velocities.Add(GetMappedFieldValue(dataRow, streamTablePrefix, "FlowVelD"));
            }
            
            return velocities;
        }

        // 动态生成报告列标题（根据设备类型）
        private List<string> GetVelocityColumnHeaders()
        {
            if (isRMGDevice)
            {
                return new List<string> { "P1Velocity", "P2Velocity", "P3Velocity", "P4Velocity", "P5Velocity", "P6Velocity" };
            }
            else
            {
                return new List<string> { "FlowVelA", "FlowVelB", "FlowVelC", "FlowVelD" };
            }
        }
    }
}

// 关键修正说明：
// 1. 对于标准字段（A/B/C/D），使用映射机制转换为RMG字段名
// 2. 对于RMG特有字段（P5/P6），直接通过字段名访问
// 3. 程序逻辑需要主动处理RMG的6路数据vs标准的4路数据
// 4. 报告生成需要根据设备类型动态调整列数和列名 