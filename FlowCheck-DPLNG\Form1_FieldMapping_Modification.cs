using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace FlowCheck_DPLNG
{
    public partial class Form1 : Form
    {
        // 添加字段映射相关成员变量
        private string currentDeviceManufacturer = "";
        private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
        private bool isRMGDevice = false;

        // 初始化字段映射字典
        private void InitializeFieldMappings()
        {
            fieldMappings.Clear();
            
            if (isRMGDevice)
            {
                // RMG设备的字段映射规则
                fieldMappings["FlowVelA"] = "P1Velocity";
                fieldMappings["FlowVelB"] = "P2Velocity";
                fieldMappings["FlowVelC"] = "P3Velocity";
                fieldMappings["FlowVelD"] = "P4Velocity";
                // 添加新的P5和P6字段
                fieldMappings["FlowVelE"] = "P5Velocity";  // 如果原系统有E字段
                fieldMappings["FlowVelF"] = "P6Velocity";  // 如果原系统有F字段
                
                // 可能需要映射的其他字段（根据实际需求添加）
                fieldMappings["SndVelA"] = "P1SoundVel";
                fieldMappings["SndVelB"] = "P2SoundVel";
                fieldMappings["SndVelC"] = "P3SoundVel";
                fieldMappings["SndVelD"] = "P4SoundVel";
                
                fieldMappings["StatusA"] = "P1Status";
                fieldMappings["StatusB"] = "P2Status";
                fieldMappings["StatusC"] = "P3Status";
                fieldMappings["StatusD"] = "P4Status";
                
                // 可以根据需要添加更多映射规则
            }
            // 非RMG设备时，fieldMappings为空，使用原始字段名
        }

        // 获取映射后的字段名
        private string GetMappedFieldName(string originalFieldName)
        {
            if (fieldMappings.ContainsKey(originalFieldName))
            {
                return fieldMappings[originalFieldName];
            }
            return originalFieldName; // 未映射的字段保持原名
        }

        // 查询设备厂商名的方法
        private async Task<string> GetDeviceManufacturerAsync(string flowMeterTag)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(
                        $"SELECT DeviceManufacturer FROM {stationTableName} WHERE FlowMeterTag = @FlowMeterTag", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                        var result = await command.ExecuteScalarAsync();
                        return result?.ToString()?.Trim() ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"获取设备厂商信息失败: {ex.Message}");
                return "";
            }
        }

        // 修改后的cmdStart_Click方法
        public async void cmdStart_Click(object sender, EventArgs e)
        {
            try
            {
                // 原有的验证逻辑...
                if (cbFlowMeter.SelectedIndex <= 0 || cbFlowComputer.SelectedIndex <= 0)
                {
                    MessageBox.Show("Please select the Meter and Flow Computer.");
                    return;
                }

                cmdStart.Enabled = false;
                streamFtTableName = cbFlowMeter.Text;

                // 新增：查询设备厂商并设置字段映射
                currentDeviceManufacturer = await GetDeviceManufacturerAsync(streamFtTableName);
                isRMGDevice = currentDeviceManufacturer.Equals("RMG", StringComparison.OrdinalIgnoreCase);
                InitializeFieldMappings();

                Log.Info($"设备厂商: {currentDeviceManufacturer}, 是否为RMG设备: {isRMGDevice}");

                // 原有的其他逻辑继续...
                // ...
            }
            catch (Exception ex)
            {
                Log.Error($"cmdStart_Click error: {ex.Message}");
                MessageBox.Show($"启动失败: {ex.Message}");
            }
            finally
            {
                cmdStart.Enabled = true;
            }
        }

        // 创建安全的字段访问方法
        private double GetMappedFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string fieldName)
        {
            string mappedFieldName = GetMappedFieldName(fieldName);
            string fullFieldName = streamTablePrefix + mappedFieldName;
            
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }
            
            // 如果映射后的字段不存在，尝试原字段名（向后兼容）
            string originalFullFieldName = streamTablePrefix + fieldName;
            if (dataRow.ContainsKey(originalFullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[originalFullFieldName]);
            }
            
            // 如果都不存在，返回默认值
            Log.Warning($"字段不存在: {fullFieldName} 或 {originalFullFieldName}");
            return 0.0;
        }

        // 修改现有的数据访问代码示例（需要替换所有类似的访问）
        private void ProcessReportData()
        {
            for (int i = 0; i < _reportData.Count; i++)
            {
                // 原来的代码：
                // Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelA"]), 2);
                
                // 修改后的代码：
                var flowVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
                var flowVelB = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelB"), 2);
                var flowVelC = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelC"), 2);
                var flowVelD = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelD"), 2);
                
                // 如果是RMG设备，还可以访问额外的字段
                double flowVelE = 0, flowVelF = 0;
                if (isRMGDevice)
                {
                    flowVelE = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelE"), 2);
                    flowVelF = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelF"), 2);
                }

                // 其他字段的处理类似...
                var sndVelA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "SndVelA"), 2);
                var statusA = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "StatusA"), 2);
                
                // ... 继续处理其他字段
            }
        }
    }
}

// 使用说明：
// 1. 将上述代码添加到Form1.cs中
// 2. 替换所有 _reportData[i][streamTablePrefix + "字段名"] 的用法为 GetMappedFieldValue调用
// 3. 根据实际的RMG设备字段结构调整fieldMappings字典
// 4. 测试确保非RMG设备的功能不受影响 