using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// CheckList数据服务类，负责处理Selection_Table查询和CheckList_Table插入操作
    /// </summary>
    public class CheckListDataService
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(CheckListDataService));
        private readonly string _connectionString;

        public CheckListDataService(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentNullException(nameof(connectionString), "数据库连接字符串不能为空");
            
            _connectionString = connectionString;
        }

        /// <summary>
        /// 根据流量计算机标签和流量计标签查询Selection_Table，并将结果连同VOS参数插入到CheckList_Table
        /// </summary>
        /// <param name="streamFcTableName">流量计算机标签</param>
        /// <param name="streamFtTableName">流量计标签</param>
        /// <param name="flowMeterCalVOS">流量计计算声速</param>
        /// <param name="flowMeterUSMAvgVOS">流量计超声波平均声速</param>
        /// <param name="flowMeterDeviationVOS">流量计声速偏差</param>
        /// <param name="flowMeterCheckTime">流量计检查时间</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> InsertCheckListRecordAsync(
            string streamFcTableName,
            string streamFtTableName,
            string flowMeterCalVOS,
            string flowMeterUSMAvgVOS,
            string flowMeterDeviationVOS,
            DateTime flowMeterCheckTime)
        {
            // 参数验证
            if (string.IsNullOrEmpty(streamFcTableName))
                throw new ArgumentNullException(nameof(streamFcTableName), "流量计算机标签不能为空");
            if (string.IsNullOrEmpty(streamFtTableName))
                throw new ArgumentNullException(nameof(streamFtTableName), "流量计标签不能为空");

            Log.Info($"开始处理CheckList记录插入 - FC: {streamFcTableName}, FT: {streamFtTableName}");

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 开始事务，确保数据一致性
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 第一步：从Selection_Table查询对应记录
                            var selectionRecord = await QuerySelectionRecordAsync(connection, transaction, streamFcTableName, streamFtTableName);
                            
                            if (selectionRecord == null)
                            {
                                Log.Warn($"在Selection_Table中未找到匹配记录 - FC: {streamFcTableName}, FT: {streamFtTableName}");
                                return false;
                            }

                            // 第二步：生成新的ID
                            var newId = await GetNextCheckListIdAsync(connection, transaction);

                            // 第三步：插入到CheckList_Table（使用传入的检查时间）
                            var insertSuccess = await InsertToCheckListTableAsync(
                                connection,
                                transaction,
                                selectionRecord,
                                newId,
                                flowMeterCalVOS,
                                flowMeterUSMAvgVOS,
                                flowMeterDeviationVOS,
                                flowMeterCheckTime);

                            if (insertSuccess)
                            {
                                // 提交事务
                                transaction.Commit();
                                Log.Info($"成功插入CheckList记录 - ID: {newId}, FC: {streamFcTableName}, FT: {streamFtTableName}, 检查时间: {flowMeterCheckTime:yyyy-MM-dd HH:mm:ss}");
                                return true;
                            }
                            else
                            {
                                // 回滚事务
                                transaction.Rollback();
                                Log.Error($"插入CheckList记录失败 - FC: {streamFcTableName}, FT: {streamFtTableName}");
                                return false;
                            }
                        }
                        catch (Exception ex)
                        {
                            // 回滚事务
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                Log.Error($"数据库操作异常 - FC: {streamFcTableName}, FT: {streamFtTableName}", sqlEx);
                LogHelper.LogError(sqlEx, $"数据库操作失败，FC: {streamFcTableName}, FT: {streamFtTableName}");
                return false;
            }
            catch (Exception ex)
            {
                Log.Error($"插入CheckList记录时发生未知异常 - FC: {streamFcTableName}, FT: {streamFtTableName}", ex);
                LogHelper.LogError(ex, $"插入CheckList记录失败，FC: {streamFcTableName}, FT: {streamFtTableName}");
                return false;
            }
        }

        /// <summary>
        /// 从Selection_Table查询匹配的记录
        /// </summary>
        private async Task<SelectionRecord> QuerySelectionRecordAsync(
            SqlConnection connection, 
            SqlTransaction transaction, 
            string streamFcTableName, 
            string streamFtTableName)
        {
            const string query = @"
                SELECT id, Station, FlowComputerTag, Stream, FlowMeterTag, StationTag, DeviceManufacturer, FlowMeterSerialNumber
                FROM Selection_Table 
                WHERE FlowComputerTag = @FlowComputerTag AND FlowMeterTag = @FlowMeterTag";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@FlowComputerTag", streamFcTableName);
                command.Parameters.AddWithValue("@FlowMeterTag", streamFtTableName);

                using (var reader = await command.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        return new SelectionRecord
                        {
                            Id = Convert.ToDouble(reader["id"]),
                            Station = reader["Station"]?.ToString(),
                            FlowComputerTag = reader["FlowComputerTag"]?.ToString(),
                            Stream = reader["Stream"]?.ToString(),
                            FlowMeterTag = reader["FlowMeterTag"]?.ToString(),
                            StationTag = reader["StationTag"]?.ToString(),
                            DeviceManufacturer = reader["DeviceManufacturer"]?.ToString(),
                            FlowMeterSerialNumber = reader["FlowMeterSerialNumber"]?.ToString()
                        };
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 获取CheckList_Table的下一个ID
        /// </summary>
        private async Task<double> GetNextCheckListIdAsync(SqlConnection connection, SqlTransaction transaction)
        {
            const string query = "SELECT ISNULL(MAX(id), 0) + 1 FROM CheckList_Table";
            
            using (var command = new SqlCommand(query, connection, transaction))
            {
                var result = await command.ExecuteScalarAsync();
                return Convert.ToDouble(result);
            }
        }

        /// <summary>
        /// 插入记录到CheckList_Table
        /// </summary>
        private async Task<bool> InsertToCheckListTableAsync(
            SqlConnection connection,
            SqlTransaction transaction,
            SelectionRecord selectionRecord,
            double newId,
            string flowMeterCalVOS,
            string flowMeterUSMAvgVOS,
            string flowMeterDeviationVOS,
            DateTime flowMeterCheckTime)
        {
            const string insertQuery = @"
                INSERT INTO CheckList_Table
                (id, Station, FlowComputerTag, Stream, FlowMeterTag, StationTag, DeviceManufacturer, FlowMeterSerialNumber, FlowMeterCalVOS, FlowMeterUSMAvgVOS, FlowMeterDeviationVOS, FlowMeterCheckTime)
                VALUES
                (@id, @Station, @FlowComputerTag, @Stream, @FlowMeterTag, @StationTag, @DeviceManufacturer, @FlowMeterSerialNumber, @FlowMeterCalVOS, @FlowMeterUSMAvgVOS, @FlowMeterDeviationVOS, @FlowMeterCheckTime)";

            using (var command = new SqlCommand(insertQuery, connection, transaction))
            {
                // 添加参数
                command.Parameters.AddWithValue("@id", newId);
                command.Parameters.AddWithValue("@Station", selectionRecord.Station ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowComputerTag", selectionRecord.FlowComputerTag ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Stream", selectionRecord.Stream ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterTag", selectionRecord.FlowMeterTag ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@StationTag", selectionRecord.StationTag ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DeviceManufacturer", selectionRecord.DeviceManufacturer ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterSerialNumber", selectionRecord.FlowMeterSerialNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterCalVOS", flowMeterCalVOS ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterUSMAvgVOS", flowMeterUSMAvgVOS ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterDeviationVOS", flowMeterDeviationVOS ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FlowMeterCheckTime", flowMeterCheckTime);

                var rowsAffected = await command.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
        }

        /// <summary>
        /// Selection_Table记录模型
        /// </summary>
        private class SelectionRecord
        {
            public double Id { get; set; }
            public string Station { get; set; }
            public string FlowComputerTag { get; set; }
            public string Stream { get; set; }
            public string FlowMeterTag { get; set; }
            public string StationTag { get; set; }
            public string DeviceManufacturer { get; set; }
            public string FlowMeterSerialNumber { get; set; }
        }
    }
}
