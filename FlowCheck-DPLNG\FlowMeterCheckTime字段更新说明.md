# FlowMeterCheckTime字段更新说明

## 更新概述

根据用户要求，在CheckList_Table表中新增了`FlowMeterCheckTime`字段，用于保存检查时间。该字段作为方法的传入参数，由调用方指定具体的时间值。

## 字段详情

### 新增字段
- **字段名**: `FlowMeterCheckTime`
- **数据类型**: `datetime`
- **用途**: 记录检查操作的执行时间
- **格式**: `yyyy-MM-dd HH:mm:ss` (例如: 2025-01-20 15:15:30)
- **设置方式**: 通过方法参数传入，由调用方指定

## 代码修改详情

### 1. CheckListDataService.cs 修改

#### 修改的方法签名
```csharp
// 方法签名已修改，增加了时间参数
public async Task<bool> InsertCheckListRecordAsync(
    string streamFcTableName,
    string streamFtTableName,
    string flowMeterCalVOS,
    string flowMeterUSMAvgVOS,
    string flowMeterDeviationVOS,
    DateTime flowMeterCheckTime)  // 新增的时间参数
```

#### 内部实现修改
1. **时间参数**: 通过方法参数接收时间值
   ```csharp
   // 时间由调用方传入，不再自动获取
   DateTime flowMeterCheckTime
   ```

2. **SQL语句更新**: 在INSERT语句中添加FlowMeterCheckTime字段
   ```sql
   INSERT INTO CheckList_Table 
   (id, Station, FlowComputerTag, Stream, FlowMeterTag, StationTag, 
    DeviceManufacturer, FlowMeterSerialNumber, FlowMeterCalVOS, 
    FlowMeterUSMAvgVOS, FlowMeterDeviationVOS, FlowMeterCheckTime)
   VALUES 
   (@id, @Station, @FlowComputerTag, @Stream, @FlowMeterTag, @StationTag, 
    @DeviceManufacturer, @FlowMeterSerialNumber, @FlowMeterCalVOS, 
    @FlowMeterUSMAvgVOS, @FlowMeterDeviationVOS, @FlowMeterCheckTime)
   ```

3. **参数添加**: 添加时间参数到SQL命令
   ```csharp
   command.Parameters.AddWithValue("@FlowMeterCheckTime", flowMeterCheckTime);
   ```

4. **日志增强**: 在成功日志中包含传入的时间信息
   ```csharp
   Log.Info($"成功插入CheckList记录 - ID: {newId}, FC: {streamFcTableName}, FT: {streamFtTableName}, 检查时间: {flowMeterCheckTime:yyyy-MM-dd HH:mm:ss}");
   ```

### 2. 私有方法更新

#### InsertToCheckListTableAsync方法
- 添加了`DateTime flowMeterCheckTime`参数
- 更新了SQL插入语句
- 添加了时间参数绑定

## 使用方式

### 调用方式已更新
用户调用方式需要增加时间参数：

```csharp
// 调用方式已更新，需要传入时间参数
DateTime checkTime = DateTime.Now; // 或指定特定时间
bool success = await InsertCheckListRecordAsync(
    "WKC1_FQIC_5101",    // 流量计算机标签
    "WKC1_FT_5101",      // 流量计标签
    "350.5",             // 计算声速
    "351.2",             // 超声波平均声速
    "0.7",               // 声速偏差
    checkTime            // 检查时间（新增参数）
);
```

### 数据库记录示例
插入后的数据库记录将包含：
```
id: 1
Station: WKC1
FlowComputerTag: WKC1_FQIC_5101
Stream: 1
FlowMeterTag: WKC1_FT_5101
StationTag: WKC1
DeviceManufacturer: DANIEL
FlowMeterSerialNumber: NULL
FlowMeterCalVOS: 350.5
FlowMeterUSMAvgVOS: 351.2
FlowMeterDeviationVOS: 0.7
FlowMeterCheckTime: 2025-01-20 15:15:30  // 由调用方传入的时间
```

## 数据库表结构更新

### 完整的CheckList_Table结构
```sql
CREATE TABLE [dbo].[CheckList_Table](
    [id] [float] NOT NULL,
    [Station] [nvarchar](255) NULL,
    [FlowComputerTag] [nvarchar](255) NULL,
    [Stream] [nvarchar](255) NULL,
    [FlowMeterTag] [nvarchar](255) NULL,
    [StationTag] [varchar](50) NULL,
    [DeviceManufacturer] [varchar](50) NULL,
    [FlowMeterSerialNumber] [nvarchar](50) NULL,
    [FlowMeterCalVOS] [nvarchar](50) NULL,
    [FlowMeterUSMAvgVOS] [nvarchar](50) NULL,
    [FlowMeterDeviationVOS] [nvarchar](50) NULL,
    [FlowMeterCheckTime] [datetime] NULL,  -- 新增字段
PRIMARY KEY CLUSTERED ([id] ASC)
)
```

## 日志记录增强

### 成功日志示例
```
2025-01-20 15:15:30 [INFO] 成功插入CheckList记录 - ID: 1, FC: WKC1_FQIC_5101, FT: WKC1_FT_5101, 检查时间: 2025-01-20 15:15:30
```

### 调试信息
- 程序会在日志中记录具体的插入时间
- 便于追踪和调试数据插入操作
- 提供完整的操作审计轨迹

## 兼容性说明

### 向后兼容
- ⚠️ **重要**: 现有的调用代码需要修改
- 方法签名已更改，增加了时间参数
- 所有调用此方法的地方都需要传入时间参数

### 数据库要求
- 需要确保CheckList_Table表中存在FlowMeterCheckTime字段
- 字段类型必须是datetime
- 字段可以为NULL（程序会自动设置值）

## 测试建议

### 1. 功能测试
```csharp
// 测试时间字段是否正确设置
DateTime testTime = new DateTime(2025, 1, 20, 15, 30, 0);
bool success = await InsertCheckListRecordAsync("FC_TEST", "FT_TEST", "350", "351", "1", testTime);

// 验证数据库中的时间是否与传入时间一致
// SELECT FlowMeterCheckTime FROM CheckList_Table WHERE FlowComputerTag = 'FC_TEST'
// 应该返回: 2025-01-20 15:30:00
```

### 2. 时间精度测试
- 验证时间格式是否正确
- 确认时间精度到秒级
- 测试不同时区的兼容性

### 3. 并发测试
- 测试多个并发插入操作的时间记录
- 验证时间戳的唯一性和准确性

## 维护注意事项

1. **时间同步**: 确保服务器系统时间准确
2. **时区处理**: 时间由调用方决定，可以传入本地时间或UTC时间
3. **性能影响**: 时间字段添加对性能影响微乎其微
4. **数据清理**: 可根据FlowMeterCheckTime字段进行历史数据清理

## 总结

此次更新成功添加了FlowMeterCheckTime字段支持：
- ✅ 时间参数传入支持
- ⚠️ API签名已更改（需要更新调用代码）
- ✅ 增强日志记录
- ✅ 完善错误处理
- ✅ 详细文档更新
- ✅ 灵活的时间控制

更新后的功能完全满足用户需求，调用方可以灵活指定检查时间，准确记录每次检查操作的执行时间。
