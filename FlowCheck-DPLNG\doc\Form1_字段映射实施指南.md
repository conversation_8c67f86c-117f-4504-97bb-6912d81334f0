# FlowCheck-DPLNG RMG设备字段映射实施指南

## 1. 修改概述

为支持RMG设备的特殊字段命名，需要在Form1.cs中实现动态字段映射功能。当检测到设备厂商为"RMG"时，系统将自动使用RMG专用的字段名。

## 2. 核心修改步骤

### Step 1: 添加成员变量
在Form1类的开头添加：

```csharp
// 字段映射相关成员变量
private string currentDeviceManufacturer = "";
private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
private bool isRMGDevice = false;
```

### Step 2: 添加字段映射初始化方法
```csharp
private void InitializeFieldMappings()
{
    fieldMappings.Clear();
    
    if (isRMGDevice)
    {
        // RMG设备字段映射规则
        fieldMappings["FlowVelA"] = "P1Velocity";
        fieldMappings["FlowVelB"] = "P2Velocity";
        fieldMappings["FlowVelC"] = "P3Velocity";
        fieldMappings["FlowVelD"] = "P4Velocity";
        fieldMappings["FlowVelE"] = "P5Velocity";  // 新增
        fieldMappings["FlowVelF"] = "P6Velocity";  // 新增
        
        // 根据实际需要添加其他字段映射
        fieldMappings["SndVelA"] = "P1SoundVel";
        fieldMappings["SndVelB"] = "P2SoundVel";
        fieldMappings["SndVelC"] = "P3SoundVel";
        fieldMappings["SndVelD"] = "P4SoundVel";
        // ... 更多映射规则
    }
}
```

### Step 3: 添加字段名转换方法
```csharp
private string GetMappedFieldName(string originalFieldName)
{
    return fieldMappings.ContainsKey(originalFieldName) 
        ? fieldMappings[originalFieldName] 
        : originalFieldName;
}
```

### Step 4: 添加设备厂商查询方法
```csharp
private async Task<string> GetDeviceManufacturerAsync(string flowMeterTag)
{
    try
    {
        using (var connection = new SqlConnection(ConnectionString))
        {
            await connection.OpenAsync();
            using (var command = new SqlCommand(
                $"SELECT DeviceManufacturer FROM {stationTableName} WHERE FlowMeterTag = @FlowMeterTag", 
                connection))
            {
                command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                var result = await command.ExecuteScalarAsync();
                return result?.ToString()?.Trim() ?? "";
            }
        }
    }
    catch (Exception ex)
    {
        Log.Error($"获取设备厂商信息失败: {ex.Message}");
        return "";
    }
}
```

### Step 5: 修改cmdStart_Click方法
在现有的cmdStart_Click方法中，在设置streamFtTableName之后添加：

```csharp
// 在 streamFtTableName = cbFlowMeter.Text; 之后添加：

// 查询设备厂商并设置字段映射
currentDeviceManufacturer = await GetDeviceManufacturerAsync(streamFtTableName);
isRMGDevice = currentDeviceManufacturer.Equals("RMG", StringComparison.OrdinalIgnoreCase);
InitializeFieldMappings();

Log.Info($"设备厂商: {currentDeviceManufacturer}, 是否为RMG设备: {isRMGDevice}");
```

### Step 6: 创建安全的字段访问方法
```csharp
private double GetMappedFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix, string fieldName)
{
    string mappedFieldName = GetMappedFieldName(fieldName);
    string fullFieldName = streamTablePrefix + mappedFieldName;
    
    if (dataRow.ContainsKey(fullFieldName))
    {
        return ParseDoubleOrDefault(dataRow[fullFieldName]);
    }
    
    // 向后兼容：如果映射字段不存在，尝试原字段名
    string originalFullFieldName = streamTablePrefix + fieldName;
    if (dataRow.ContainsKey(originalFullFieldName))
    {
        return ParseDoubleOrDefault(dataRow[originalFullFieldName]);
    }
    
    Log.Warning($"字段不存在: {fullFieldName} 或 {originalFullFieldName}");
    return 0.0;
}
```

## 3. 需要修改的具体位置

### 在Form1.cs中找到以下代码行并替换：

**原始代码 (约第999行)：**
```csharp
Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelA"]), 2);
```

**修改为：**
```csharp
Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelA"), 2);
```

### 需要批量替换的所有字段访问：

1. **FlowVel系列：** FlowVelA, FlowVelB, FlowVelC, FlowVelD
2. **SndVel系列：** SndVelA, SndVelB, SndVelC, SndVelD  
3. **Status系列：** StatusA, StatusB, StatusC, StatusD
4. **PctGood系列：** PctGoodA1, PctGoodA2, PctGoodB1, PctGoodB2, PctGoodC1, PctGoodC2, PctGoodD1, PctGoodD2
5. **Gain系列：** GainA1, GainA2, GainB1, GainB2, GainC1, GainC2, GainD1, GainD2
6. **SNR系列：** SNRA1, SNRA2, SNRB1, SNRB2, SNRC1, SNRC2, SNRD1, SNRD2
7. **Turbulence系列：** TurbulenceA, TurbulenceB, TurbulenceC, TurbulenceD

### 批量替换正则表达式：

**查找：**
```regex
ParseDoubleOrDefault\(_reportData\[i\]\[streamTablePrefix \+ "([^"]+)"\]\)
```

**替换为：**
```csharp
GetMappedFieldValue(_reportData[i], streamTablePrefix, "$1")
```

## 4. RMG设备专用字段扩展

对于RMG设备的P5Velocity和P6Velocity等新增字段，在数据处理代码中添加：

```csharp
// 如果是RMG设备，处理额外的字段
if (isRMGDevice)
{
    var flowVelE = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelE"), 2);
    var flowVelF = Math.Round(GetMappedFieldValue(_reportData[i], streamTablePrefix, "FlowVelF"), 2);
    // 将这些值添加到报告或显示逻辑中
}
```

## 5. 测试验证

### 5.1 非RMG设备测试
- 确保现有功能完全正常
- 验证所有原有字段访问正确

### 5.2 RMG设备测试  
- 在Selection_Table中设置测试记录，DeviceManufacturer = "RMG"
- 验证字段映射正确工作
- 确认P5Velocity和P6Velocity字段能正确读取

### 5.3 错误处理测试
- 测试字段不存在的情况
- 测试数据库连接失败的情况
- 验证日志记录正确

## 6. 扩展建议

### 6.1 配置文件化
可以考虑将字段映射规则移到配置文件中：

```xml
<FieldMappings>
  <Manufacturer name="RMG">
    <Mapping from="FlowVelA" to="P1Velocity"/>
    <Mapping from="FlowVelB" to="P2Velocity"/>
    <!-- 更多映射 -->
  </Manufacturer>
</FieldMappings>
```

### 6.2 支持更多厂商
框架设计支持添加其他厂商的字段映射规则：

```csharp
else if (currentDeviceManufacturer.Equals("DANIEL", StringComparison.OrdinalIgnoreCase))
{
    // DANIEL设备的字段映射
}
```

## 7. 性能影响评估

- **内存影响：** 添加少量字典存储，影响极小
- **性能影响：** 字段名查找为O(1)操作，性能影响忽略不计  
- **兼容性：** 完全向后兼容，不会影响现有功能

## 8. 实施注意事项

1. **备份现有代码** - 修改前务必备份
2. **逐步测试** - 每修改一部分就测试一次
3. **日志记录** - 确保关键操作都有日志记录
4. **错误处理** - 字段不存在时的优雅降级
5. **文档更新** - 更新相关技术文档

这个方案的最大优势是**最小侵入性**，现有的非RMG设备功能完全不受影响，同时为RMG设备提供了完整的字段映射支持。 