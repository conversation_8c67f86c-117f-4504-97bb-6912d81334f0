﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using log4net;
using OfficeOpenXml;
using OpenTK.Graphics.ES10;
using LicenseContext = OfficeOpenXml.LicenseContext;
using static FlowCheck_DPLNG.DataProcessor;
using Panel = System.Windows.Forms.Panel;
using Label = System.Windows.Forms.Label;
using Timer = System.Timers.Timer;
using Color = System.Drawing.Color;

namespace FlowCheck_DPLNG
{
    public partial class Form1 : Form
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(Form1));
        private readonly Dictionary<string, string> dictHisData = new Dictionary<string, string>();
        private DatabasePoller poller;
        private StabilityThresholdReader thresholdReader;
        private readonly StationConfigManager _configManager; // read station and stream config from json file

        // used for RMG table 
        private string currentDeviceManufacturer = "";
        private Dictionary<string, string> fieldMappings = new Dictionary<string, string>();
        private bool isRMGDevice = false;

        private bool isSystemStatusGood = false;

        string excelTemplatePath =
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SOSCheckReportTemplate.xlsx");

        string excelTemplatePathRMG =
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SOSCheckReportTemplate_RMG.xlsx");


        private readonly Dictionary<string, (Panel panel, Label label)> fieldPanels =
            new Dictionary<string, (Panel, Label)>();

        private Dictionary<string, Panel> keywordPanels = new Dictionary<string, Panel>();

        public static Dictionary<string, FieldTypeStatus> fieldTypeStatuses { get; } =
            new Dictionary<string, FieldTypeStatus>();

        //private readonly Dictionary<string, FieldTypeStatus> fieldTypeStatuses = new Dictionary<string, FieldTypeStatus>
        //{
        //    { "SLCT", new FieldTypeStatus() },
        //    { "PressInuse_Check", new FieldTypeStatus() },
        //    { "TempInuse_Check", new FieldTypeStatus() },
        //    { "USMAVGVOS_Check", new FieldTypeStatus() }
        //};

        private readonly double Pb = 101.325;
        private readonly Dictionary<string, string> result = new Dictionary<string, string>();
        private readonly Dictionary<string, string> resultFT = new Dictionary<string, string>();
        private readonly double Tb = 20.0;
        private int count;

        private int dataCollectionDurationMiniutes;

        public string ConnectionString;
        private string stationTableName;

        private readonly StationDataManager _stationManager;
        private Dictionary<string, string> _tagStreamMapping;

        private double Df;
        private double Ds;
        private double extDiam;

        private double GasVel;

        private List<string> fcList;

        private double intDiam;

        // 用来存放报表数据
        private List<Dictionary<string, string>> _reportData = new List<Dictionary<string, string>>();
        private string _excelFilePathToOpen;
        private string _reportFolderPath;


        private DateTime reportTime;
        private string streamFcTableName;
        private string streamFtTableName;
        private string historyTagSuffix;
        private string streamTablePrefix;

        private Timer waitHisDataTimer = new Timer();

        private XmlConfigReader configReader;
        private DataPlotter dataPlotter;
        private CheckListDataService checkListDataService;

        public Form1()
        {
            InitializeComponent();
            Log.Info("Program start!");

            //InitializeFieldPanels();

            configReader = new XmlConfigReader("Config/AppConfig.xml");
            ConnectionString = configReader.GetConnectionString();
            _reportFolderPath = configReader.GetReportPath();
            stationTableName = configReader.GetStationDataTableName();

            _stationManager = new StationDataManager(ConnectionString, stationTableName);
            _configManager = new StationConfigManager();
            _tagStreamMapping = new Dictionary<string, string>();

            dataPlotter = new DataPlotter();
            checkListDataService = new CheckListDataService(ConnectionString);
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            Clear();
            cbTime.SelectedIndex = 2;
            dataCollectionDurationMiniutes = 3;
            count = 60 * dataCollectionDurationMiniutes;

            await LoadStationNameData();

            thresholdReader = new StabilityThresholdReader();
            thresholdReader.ReadThresholds();


            cmdGenerateReport.Enabled = false;

            Text2.Leave += Text2_LostFocus;

            foreach (Control c in groupBox1.Controls)
            {
                var tp = c.GetType();
                var szCtrlName = c.Name;
                if (tp.Name == "TextBox" && szCtrlName.IndexOf("textBox") != -1)
                {
                    c.KeyPress += textBox_KeyPress;
                    c.TextChanged += textBox_TextChanged;
                    c.Leave += textBox_LostFocus;
                }
            }
        }

        [DllImport(
            @"Lib\234dll.dll",
            EntryPoint = "?AGA10_Init@@YGHXZ",
            ExactSpelling = true,
            CharSet = CharSet.Ansi,
            SetLastError = true
        )]
        private static extern long AGA10_Init();

        [DllImport(
            @"Lib\234dll.dll",
            EntryPoint = "?AGA10_UnInit@@YGHXZ",
            ExactSpelling = true,
            CharSet = CharSet.Ansi,
            SetLastError = true
        )]
        private static extern long AGA10_UnInit();

        [DllImport(
            @"Lib\234dll.dll",
            EntryPoint = "?Crit@@YGNPAUtagAGA10STRUCT@@N@Z",
            CallingConvention = CallingConvention.StdCall
        )]
        private static extern double Crit(ref AGA10STRUCT AGAPtr, double dPlenumVelocity);

        private void InitializeFieldPanels(string tagNameSuffix)
        {
            //AddPanelWithLabel("SystemStatus", panelSystem);
            //AddPanelWithLabel("StatusA", panelStatusA);
            //AddPanelWithLabel("StatusB", panelStatusB);
            //AddPanelWithLabel("StatusC", panelStatusC);
            //AddPanelWithLabel("StatusD", panelStatusD);

            AddPanelWithLabel("SLCT", panelGC);
            AddPanelWithLabel("PressInuse", panelPressInuse);
            AddPanelWithLabel("TempInuse", panelTempInuse);
            AddPanelWithLabel("USMAvgVOS", panelUSMAvgVOS);
        }

        private void AddPanelWithLabel(string key, Panel panel)
        {
            // 清空Panel中的现有控件，避免重复添加Label导致显示异常
            panel.Controls.Clear();

            var label = new Label
            {
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                Font = new Font(Font.FontFamily, 10, System.Drawing.FontStyle.Bold)
            };
            panel.Controls.Add(label);
            fieldPanels[key] = (panel, label);
        }

        private void DebugPrintFieldTypeStatuses()
        {
            foreach (var kvp in fieldTypeStatuses)
                Debug.WriteLine(
                    $"Field Type: {kvp.Key}, Total: {kvp.Value.TotalFields}, Checked: {kvp.Value.CheckedFields}, IsAllStable: {kvp.Value.IsAllStable}");
        }

        private void UpdatePanelColor(string field, bool isStable)
        {
            foreach (var fieldType in fieldTypeStatuses.Keys)
                if (field.Contains(fieldType))
                {
                    BeginInvoke((MethodInvoker)delegate
                    {
                        var (panel, label) = fieldPanels[fieldType];
                        panel.BackColor = isStable ? System.Drawing.Color.Green : Color.Red;
                        label.ForeColor = Color.White;
                        label.Text = isStable ? "Passed" : "Failed";
                        Debug.WriteLine($"Panel color updated for {fieldType}: {panel.BackColor}");
                    });
                    break;
                }
        }


        private void HandleFinalResult(
            (bool isStable, List<string> unstableFields, Dictionary<string, FieldStatistics> fieldStats) result)
        {
            Debug.WriteLine("Handling final result");
            Debug.WriteLine($"Is stable: {result.isStable}");
            Debug.WriteLine($"Unstable fields count: {result.unstableFields.Count}");

            if (result.isStable)
            {
                lbMessage.ForeColor = Color.Green;
                lbMessage.Text = "Parameters stability check PASSED.";
            }
            else
            {
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "Parameters stability check FAILED.";
                Log.Error($"Some fields are unstable: {string.Join(", ", result.unstableFields)}");
            }

            // 更新Panel的详细信息
            foreach (var fieldType in fieldTypeStatuses.Keys)
            {
                var status = fieldTypeStatuses[fieldType];
                var (panel, label) = fieldPanels[fieldType];

                var stats = result.fieldStats
                    .Where(kv => kv.Key.Contains(fieldType))
                    .Select(kv => kv.Value)
                    .ToList();

                if (stats.Any())
                {
                    var maxRange = stats.Max(s => s.Range);
                    var maxStdDev = stats.Max(s => s.StandardDeviation);

                    label.Text =
                        $"Max Dev: {maxRange:F3}\nMaxStdDev: {maxStdDev:F3}";
                }
                else
                {
                    label.Text = "Get data FAILED.";
                }

                Debug.WriteLine($"Updated panel for {fieldType}: {label.Text}");
            }

            DebugPrintFieldTypeStatuses();
        }


        //private bool StabilityCheck(float[] arryData, float stdDev)
        //{
        //    var xSum = 0F;
        //    var xAvg = 0F;
        //    var sSum = 0F;
        //    var tmpStDev = 0F;

        //    if (arryData != null)
        //    {
        //        var arrNum = arryData.Length;

        //        for (var i = 0; i < arrNum; i++) xSum += arryData[i];

        //        xAvg = xSum / arrNum;

        //        for (var j = 0; j < arrNum; j++) sSum += (arryData[j] - xAvg) * (arryData[j] - xAvg);

        //        tmpStDev = Convert.ToSingle(Math.Sqrt(sSum / arrNum).ToString());
        //    }
        //    else
        //    {
        //        return false;
        //    }

        //    if (tmpStDev <= stdDev) return true;

        //    return false;
        //}

        //private bool ParameterCheck(DateTime dt, string varName, string targetTable, float stdDev)
        //{
        //    if (StabilityCheck(Get5MinHisDataFromOE(dt, varName, targetTable), stdDev))
        //    {
        //        return true;
        //    }
        //    return false;
        //}

        private DataTable GetRawDataFromOE(DateTime dt, string targetTable)
        {
            var queryTime = dt.AddHours(-8).AddMinutes(-1).ToString("dd-MMM-yyyy HH:mm:00");
            //string sqlStr = "select name,value from " + targetTable + " where timestamp='" + queryTime + "'";
            var sqlStr = "select name,value from " + targetTable;
            var resultTable = SQLData.GetRowsFromOE(sqlStr, "resultTableFromOE");
            return resultTable;
        }

        //private ArrayList Get5MinHisDataFromOE(
        //    DateTime dt,
        //    string varName,
        //    string targetTable,
        //    string streamName
        //)
        //{
        //    var endTime = dt.AddHours(-8).AddMinutes(-0.5).ToString("dd-MMM-yyyy HH:mm:00");
        //    var startTime = dt.AddHours(-8)
        //        .AddMinutes(-0.5 - dataCollectionDurationMiniutes)
        //        .ToString("dd-MMM-yyyy HH:mm:00");
        //    var sqlStr =
        //        "select name,timestamp,value from "
        //        + targetTable
        //        + " where name like '"
        //        + streamName
        //        + ":"
        //        + varName
        //        + "%' and"
        //        + " timestamp>='"
        //        + startTime
        //        + "' and timestamp<='"
        //        + endTime
        //        + "' order by timestamp asc";
        //    var resultTable = SQLData.GetRowsFromOE(sqlStr, "resultTableFromOE");
        //    var arrayList = new ArrayList();
        //    ;

        //    dictHisData.Clear();
        //    if (resultTable != null && resultTable.Rows.Count > 0)
        //    {
        //        for (var i = 0; i < resultTable.Rows.Count; i++) arrayList.Add(resultTable.Rows[i]["value"].ToString());

        //        return arrayList;
        //    }

        //    lbMessage.ForeColor = Color.Red;
        //    lbMessage.Text = "Error:流量计参数数据获取失败!";
        //    return null;
        //}

        //private Dictionary<string, string> GetSpecificParaDict(string name, DataTable dt)
        //{
        //    var paraDict = new Dictionary<string, string>();

        //    for (var i = 0; i < dt.Rows.Count; i++)
        //        if (dt.Rows[i]["name"].ToString() == name)
        //            paraDict.Add(name, dt.Rows[i]["value"].ToString());

        //    return paraDict;
        //}

        //Calculate
        public void CmdCal_Click(object sender, EventArgs e)
        {
            double c1 = Convert.ToDouble(textBox0.Text);
            cmdCal.Enabled = false;
            if (c1 <= 5)
            {
                MessageBox.Show("Please check the COMPONENT.");
                cmdCal.Enabled = true;
                return;
            }

            var A10 = new AGA10STRUCT();
            var Db = 0.0;
            //int i;
            AGA10_Init();

            A10.lStatus = 9000;
            A10.Methane = c1 / 100;
            A10.Nitrogen = Convert.ToDouble(textBox12.Text) / 100;
            A10.CO2 = Convert.ToDouble(textBox13.Text) / 100;
            A10.Ethane = Convert.ToDouble(textBox1.Text) / 100;
            A10.Propane = Convert.ToDouble(textBox2.Text) / 100;
            A10.H2O = Convert.ToDouble(textBox15.Text) / 100;
            A10.H2S = Convert.ToDouble(textBox14.Text) / 100;
            A10.H2 = Convert.ToDouble(textBox19.Text) / 100;
            A10.CO = Convert.ToDouble(textBox18.Text) / 100;
            A10.O2 = Convert.ToDouble(textBox17.Text) / 100;
            A10.i_Butane = Convert.ToDouble(textBox4.Text) / 100;
            A10.n_Butane = Convert.ToDouble(textBox3.Text) / 100;
            var moleNeoC5 = Convert.ToDouble(Text2.Text) / 100;
            var moleiC5 = Convert.ToDouble(textBox6.Text) / 100;
            A10.i_Pentane = moleiC5 + moleNeoC5; //add neoC5 to iC5
            A10.n_Pentane = Convert.ToDouble(textBox5.Text) / 100;
            A10.n_Hexane = Convert.ToDouble(textBox7.Text) / 100;
            A10.n_Heptane = Convert.ToDouble(textBox8.Text) / 100;
            A10.n_Octane = Convert.ToDouble(textBox9.Text) / 100;
            A10.n_Nonane = Convert.ToDouble(textBox10.Text) / 100;
            A10.n_Decane = Convert.ToDouble(textBox11.Text) / 100;
            A10.He = Convert.ToDouble(textBox16.Text) / 100;
            A10.Ar = Convert.ToDouble(textBox20.Text) / 100;

            A10.dPb = 101.325 * 1000; //pa
            // the unit of text25.text is already Mpa
            A10.dPf = Convert.ToDouble(Text25.Text) * 1000000; //pa

            A10.dTb = 20.0 + 273.15;
            A10.dTf = Convert.ToDouble(Text24.Text) + 273.15;

            if (Math.Abs(Convert.ToDouble(txtTotal.Text) - 100.0) > 0.5)
            {
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "Sum of component not equal to 100！";
            }
            else
            {
                Crit(ref A10, Db);

                txtCalSOS.Text = string.Format("{0:F4}", A10.dSOS);
                Df = A10.dRhof;
                Ds = A10.dRhob;
                //Text28.Text = string.Format("{0:F8}", A10.dRD_Ideal);
                //Text29.Text = string.Format("{0:F8}", A10.dRD_Real);
                //txtCalZb.Text = string.Format("{0:F8}", A10.dZb);
                //txtCalZf.Text = string.Format("{0:F8}", A10.dZf);

                AGA10_UnInit();
            }

            if ((Math.Round(Tb) != 20) | (Math.Round(Pb, 3) != 101.325))
            {
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "Please check the base condition setting!";
            }
            else
            {
                DevCalculation();
            }

            cmdCal.Enabled = true;
        }

        //Clear
        private void DevCalculation()
        {
            try
            {
                double dev = Math.Round(
                    (ParseDoubleOrDefault(txtFCSOS.Text) - ParseDoubleOrDefault(txtCalSOS.Text))
                    / (
                        ParseDoubleOrDefault(txtCalSOS.Text) == 0.0
                            ? 1
                            : ParseDoubleOrDefault(txtCalSOS.Text)
                    )
                    * 100,
                    5
                );

                txtDevSOS.Text = dev.ToString();
                if (Math.Abs(dev) > 0.2)
                {
                    txtDevSOS.BackColor = Color.OrangeRed;
                }
                else
                {
                    txtDevSOS.BackColor = Color.LimeGreen;
                }
            }
            catch (Exception ex)
            {
                lbMessage.Text = "Error: " + ex.Message;
                txtDevSOS.BackColor = Color.Red;
            }
        }

        private void CalTotComp()
        {
            var dSum =
                Convert.ToDouble(textBox0.Text)
                + Convert.ToDouble(textBox1.Text)
                + Convert.ToDouble(textBox2.Text)
                + Convert.ToDouble(textBox3.Text)
                + Convert.ToDouble(textBox4.Text)
                + Convert.ToDouble(textBox5.Text)
                + Convert.ToDouble(textBox6.Text)
                + Convert.ToDouble(textBox7.Text)
                + Convert.ToDouble(textBox8.Text)
                + Convert.ToDouble(textBox9.Text)
                + Convert.ToDouble(textBox10.Text)
                + Convert.ToDouble(textBox11.Text)
                + Convert.ToDouble(textBox12.Text)
                + Convert.ToDouble(textBox13.Text)
                + Convert.ToDouble(textBox14.Text)
                + Convert.ToDouble(textBox15.Text)
                + Convert.ToDouble(textBox16.Text)
                + Convert.ToDouble(textBox17.Text)
                + Convert.ToDouble(textBox18.Text)
                + Convert.ToDouble(textBox19.Text)
                + Convert.ToDouble(textBox20.Text)
                + Convert.ToDouble(Text2.Text);
            txtTotal.Text = dSum.ToString();
        }

        public double RealCV(
            double moleC1,
            double moleC2,
            double moleC3,
            double moleNC4,
            double moleIC4,
            double moleNC5,
            double moleIC5,
            double moleNeoC5,
            double moleC6,
            double moleN2,
            double moleCO2,
            double moleH2O,
            double moleH2S,
            double moleHe,
            double moleO2,
            double moleCO,
            double moleH2,
            double moleAr,
            double moleC7,
            double moleC8,
            double moleC9,
            double moleC10,
            float temp
        )
        {
            double returnValue = 0;

            //Dim sList0(10) As Double
            var sList1 = "";
            var sList2 = "";
            var sList3 = "";
            var sList4 = "";
            var sList5 = "";
            var sList6 = "";
            var sList7 = "";
            //Dim moleC1, moleC2, moleC3, moleNC4, moleIC4, moleNC5, moleIC5, moleNeoC5, moleC6P, moleN2, moleCO2, moleH20 As Double

            string[] moleMass;
            var moleFraction = new double[22];
            string[] temp15zCompress;
            string[] temp20zCompress;
            string[] temp15Factor;
            string[] temp20Factor;
            string[] temp15Superi;
            string[] temp20Superi;

            var temp15XjHj = new double[11];
            double sum15XjHj = 0;
            var temp20XjHj = new double[22];
            double sum20XjHj = 0;

            var temp15XjBj = new double[11];
            double sum15XjBj = 0;
            var temp20XjBj = new double[22];
            double sum20XjBj = 0;

            double temp15IdealCV = 0;
            double temp20IdealCV = 0;
            //double temp15RealCV;
            //double temp20RealCV;
            double temp15StdCompress = 0;
            double temp20StdCompress = 0;

            var i = 0;
            var j = 0;

            moleFraction[0] = moleC1 / 100;
            moleFraction[1] = moleC2 / 100;
            moleFraction[2] = moleC3 / 100;
            moleFraction[3] = moleNC4 / 100;
            moleFraction[4] = moleIC4 / 100;
            moleFraction[5] = moleNC5 / 100;
            moleFraction[6] = moleIC5 / 100;
            moleFraction[7] = moleC6 / 100;
            moleFraction[8] = moleN2 / 100;
            moleFraction[9] = moleCO2 / 100;
            moleFraction[10] = moleH2O / 100;
            moleFraction[11] = moleH2S / 100;
            moleFraction[12] = moleHe / 100;
            moleFraction[13] = moleO2 / 100;
            moleFraction[14] = moleCO / 100;
            moleFraction[15] = moleH2 / 100;
            moleFraction[16] = moleAr / 100;
            ;
            moleFraction[17] = moleNeoC5 / 100;
            moleFraction[18] = moleC7 / 100;
            ;
            moleFraction[19] = moleC8 / 100;
            moleFraction[20] = moleC9 / 100;
            moleFraction[21] = moleC10 / 100;

            sList1 =
                "16.043, 30.07, 44.097, 58.123, 58.123, 72.15, 72.15, 72.15, 86.177, 28.0135, 44.01,18.0153,34.082,4.0026,31.9988,28.01,2.0159,39.948,72.15,100.204,114.231,128.258,142.285";
            sList2 = "0.998,0.9915,0.9821,0.965,0.968,0.937,0.948,0.955,0.913,0.9997,0.9944,0.945";
            sList3 =
                "0.9981,0.992,0.9834,0.9682,0.971,0.945,0.953,0.959, 0.919,0.9997,0.9947,0.952,0.99,1.0005,0.9993,0.9996,1.0006,0.9993,0.959,0.876,0.817,0.735,0.623";
            sList4 =
                "0.0447,0.0922,0.1338,0.1871,0.1789,0.251,0.228,0.295,0.0173,0.0748,0.2345,0.1,0.0002,0.0283,0.0224,-0.0048,0.0283,0.2121,0.3661,0.445,0.5385,0.645";
            sList5 =
                "0.0436,0.0894,0.1288,0.1783,0.1703,0.2345,0.2168,0.2846,0.0173,0.0728,0.2191,0.1,0,0.0265,0.02,-0.0051,0.0265,0.2025,0.3521,0.4278,0.5148,0.614";
            sList6 =
                "891.56,1562.14,2221.1,2879.76,2870.58,3538.6,3531.68,4198.24,0,0,44.433,562.38,0,0,282.91,286.15,0,3517.43,4857.18,5516.01,6175.82,6834.9";
            sList7 =
                "891.09,1561.41,2220.13,2878.57,2869.38,3537.17,3530.24,4196.58,0,0,44.224,562.19,0,0,282.95,285.99,0,3516.01,4855.29,5513.88,6173.46,6832.31";

            moleMass = sList1.Split(',');
            temp15zCompress = sList2.Split(',');
            temp20zCompress = sList3.Split(',');
            temp15Factor = sList4.Split(',');
            temp20Factor = sList5.Split(',');
            temp15Superi = sList6.Split(',');
            temp20Superi = sList7.Split(',');

            //calculate Xj*Hj in 15 deg
            for (i = 0; i <= moleMass.Length - 1 - 1; i++)
            {
                for (j = 0; j <= moleMass.Length - 1 - 1; j++)
                    temp15XjHj[j] = moleFraction[j] * Convert.ToDouble(temp15Superi[j]);

                sum15XjHj = sum15XjHj + temp15XjHj[i];
            }

            //calculate Xj*Hj in 20 deg
            for (i = 0; i <= moleMass.Length - 1 - 1; i++)
            {
                for (j = 0; j <= moleMass.Length - 1 - 1; j++)
                    temp20XjHj[j] = moleFraction[j] * Convert.ToDouble(temp20Superi[j]);

                sum20XjHj = sum20XjHj + temp20XjHj[i];
            }

            //calculate Xj*Bj in 15 deg
            for (i = 0; i <= moleFraction.Length - 1; i++)
            {
                for (j = 0; j <= moleFraction.Length - 1; j++)
                    temp15XjBj[j] = moleFraction[j] * Convert.ToDouble(temp15Factor[j]);

                sum15XjBj = sum15XjBj + temp15XjBj[i];
            }

            //calculate Xj*Bj in 20 deg
            for (i = 0; i <= moleFraction.Length - 1; i++)
            {
                for (j = 0; j <= moleFraction.Length - 1; j++)
                    temp20XjBj[j] = moleFraction[j] * Convert.ToDouble(temp20Factor[j]);

                sum20XjBj = sum20XjBj + temp20XjBj[i];
            }

            if (temp == 15)
            {
                temp15StdCompress = 1 - sum15XjBj * sum15XjBj;
                temp15IdealCV = sum15XjHj * 101.325 / (8.31451 * 288.15);
                returnValue = temp15IdealCV / temp15StdCompress;
            }
            else if (temp == 20)
            {
                temp20StdCompress = 1 - sum20XjBj * sum20XjBj;
                temp20IdealCV = sum20XjHj * 101.325 / (8.31451 * 293.15);
                return temp20IdealCV / temp20StdCompress;
            }

            return returnValue;
        }


        private void Clear()
        {
            textBox0.Text = "0";
            textBox1.Text = "0";
            textBox2.Text = "0";
            textBox3.Text = "0";
            textBox4.Text = "0";
            textBox5.Text = "0";
            textBox6.Text = "0";
            textBox7.Text = "0";
            textBox8.Text = "0";
            textBox9.Text = "0";
            textBox10.Text = "0";
            textBox11.Text = "0";
            textBox12.Text = "0";
            textBox13.Text = "0";
            textBox14.Text = "0";
            textBox15.Text = "0";
            textBox16.Text = "0";
            textBox17.Text = "0";
            textBox18.Text = "0";
            textBox19.Text = "0";
            textBox20.Text = "0";
            //textBox21.Text = "0";
            Text2.Text = "0";
            //Text22.Text = "0";
            //Text23.Text = "0";
            Text24.Text = "0";
            Text25.Text = "0";
            txtTotal.Text = "0";

            txtCalSOS.Text = "0";
            //txtCalZb.Text = "0";
            //txtCalZf.Text = "0";
            //txtCalCV.Text = "0";
            //txtCalUVOL.Text = "0";
            //txtCalCVOL.Text = "0";
            //txtCalEnergy.Text = "0";
            //txtCalMass.Text = "0";

            txtFCSOS.Text = "0";
            //txtFCZb.Text = "0";
            //txtFCZf.Text = "0";
            //txtFCCV.Text = "0";
            //txtFCUVOL.Text = "0";
            //txtFCCVOL.Text = "0";
            //txtFCEnergy.Text = "0";
            //txtFCMass.Text = "0";

            txtDevSOS.Text = "0";
            //txtDevZb.Text = "0";
            //txtDevZf.Text = "0";
            //txtDevCV.Text = "0";
            //txtDevUVOL.Text = "0";
            //txtDevCVOL.Text = "0";
            //txtDevEnergy.Text = "0";
            //txtDevMass.Text = "0";

            lbDate.Text = "";
            //lbPfCheckStatus.Text = "";
            //lbTfCheckStatus.Text = "";
            //lbVelCheckStatus.Text = "";

            lbMessage.Text = "";

            cbTime.Enabled = true;

            cmdGenerateReport.Enabled = true;
            result.Clear();

            timer1.Stop();

            lblWaitTime.Text = "";
        }

        private void Text2_TextChanged(object sender, EventArgs e)
        {
            CalTotComp();
        }

        private void Text2_LostFocus(object sender, EventArgs e)
        {
            if (
                Text2.Text.Length - Text2.Text.Trim(',').Length > 1
                || Convert.ToDouble(Text2.Text) < 0
                || Math.Abs(Convert.ToDouble(Text2.Text) - 100) < 0.0000001
                || Convert.ToDouble(Text2.Text) > 100
            )
                Text2.Text = "0";
            else
                Text2.Text = string.Format("{0:F4}", Convert.ToDouble(Text2.Text));
        }

        private void textBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            int KeyAscii = e.KeyChar;
            if (KeyAscii == 8) return; //enable backspace

            if ((KeyAscii < 48) | (KeyAscii > 57) && KeyAscii != 46) e.KeyChar = (char)0;
            //KeyAscii = 0; //disable characters, only numbers and dot
        }

        private void textBox_TextChanged(object sender, EventArgs e)
        {
            var tb = (TextBox)sender;
            if (tb.Text == "")
                tb.Text = "0";
            else
                CalTotComp();
        }

        private void textBox_LostFocus(object sender, EventArgs e)
        {
            var tb = (TextBox)sender;
            var szText = tb.Text;
            if (
                szText.Length - szText.Trim(',').Length > 1
                || Convert.ToDouble(szText) <= 0
                || Math.Abs(Convert.ToDouble(szText) - 100) < 0.0000001
                || Convert.ToDouble(szText) > 100
            )
                tb.Text = "0";
            else
                tb.Text = string.Format("{0:F4}", Convert.ToDouble(szText));
        }

        private void Text22_KeyPress(object sender, KeyPressEventArgs e)
        {
            int KeyAscii = e.KeyChar;
            if (KeyAscii == 8) return; //enable backspace

            if ((KeyAscii < 48) | (KeyAscii > 57) && KeyAscii != 46) e.KeyChar = (char)0;
            //KeyAscii = 0; //disable characters, only numbers and dot
        }

        private void Text23_KeyPress(object sender, KeyPressEventArgs e)
        {
            int KeyAscii = e.KeyChar;
            if (KeyAscii == 8) return; //enable backspace

            if ((KeyAscii < 48) | (KeyAscii > 57) && KeyAscii != 46) e.KeyChar = (char)0;
            //KeyAscii = 0; //disable characters, only numbers and dot
        }

        private void Text24_KeyPress(object sender, KeyPressEventArgs e)
        {
            int KeyAscii = e.KeyChar;
            if (KeyAscii == 8) return; //enable backspace

            if ((KeyAscii < 48) | (KeyAscii > 57) && KeyAscii != 46) e.KeyChar = (char)0;
            //KeyAscii = 0; //disable characters, only numbers and dot
        }

        private void Text25_KeyPress(object sender, KeyPressEventArgs e)
        {
            int KeyAscii = e.KeyChar;
            if (KeyAscii == 8) return; //enable backspace

            if ((KeyAscii < 48) | (KeyAscii > 57) && KeyAscii != 46) e.KeyChar = (char)0;
            //KeyAscii = 0; //disable characters, only numbers and dot
        }

        //private void GenerateSOSCheckReport()
        //{
        //    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        //    _excelFilePathToOpen = "";

        //    var newReportName =
        //        "GUSM_SOSCHECK_"
        //        + cbFlowMeter.Text
        //        + "_"
        //        + reportTime.ToString("yyyy-MM-dd HH-mm-ss")
        //        + ".xlsx";
        //    var savedPath = @"C:\Reports\" + cbStation.Text.Trim() + @"\" + cbFlowMeter.Text.Trim() +
        //                    @"\SOSCheckReport\";
        //    //var serialNo = StreanNoToSN(cbFlowMeter.Text.Substring(8));

        //    if (!Directory.Exists(savedPath + newReportName))
        //    {
        //        Directory.CreateDirectory(savedPath);
        //        var newFile = new FileInfo(savedPath + newReportName);
        //        _excelFilePathToOpen = savedPath + newReportName;

        //        if (isRMGDevice)
        //        {
        //            var templateDailyReport = new FileInfo(excelTemplatePathRMG);

        //            using (var excelPackage = new ExcelPackage(newFile, templateDailyReport))
        //            {
        //                excelPackage.Workbook.Worksheets[2].Cells["B5"].Value = textBox0.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B6"].Value = textBox1.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B7"].Value = textBox2.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B8"].Value = textBox3.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B9"].Value = textBox4.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B10"].Value = textBox5.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B11"].Value = textBox6.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B12"].Value = Text2.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B13"].Value = textBox7.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B14"].Value = textBox12.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B15"].Value = textBox13.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D24"].Value = textBox14.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D25"].Value = textBox15.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D26"].Value = textBox17.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D27"].Value = textBox18.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D28"].Value = textBox19.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D29"].Value = textBox20.Text;

        //                excelPackage.Workbook.Worksheets[0].Cells["B1"].Value = cbStation.Text;
        //                excelPackage.Workbook.Worksheets[0].Cells["B2"].Value = cbFlowMeter.Text;
        //                excelPackage.Workbook.Worksheets[0].Cells["B7"].Value = ""; // SN
        //                excelPackage.Workbook.Worksheets[0].Cells["K1"].Value = DateTime.Now.ToLongDateString();
        //                excelPackage.Workbook.Worksheets[0].Cells["K2"].Value = DateTime.Now.ToLongTimeString();

        //                //string descCalMethod = float.Parse(resultFT[streamTablePrefix + "CalMethod"]) switch
        //                //{
        //                //    0.0f => "None",
        //                //    1.0f => "Polynomial",
        //                //    2.0f => "Piece-wise linear",
        //                //    _ => "None"
        //                //};
        //                //excelPackage.Workbook.Worksheets[0].Cells["G38"].Value = descCalMethod;

        //                //excelPackage.Workbook.Worksheets[0].Cells["B59"].Value =
        //                //    resultFT[streamTablePrefix + "QMeterValidity"];


        //                try
        //                {
        //                    //excelPackage.Workbook.Worksheets[0].Cells["B8"].Value = Math.Round(intDiam, 6).ToString();
        //                    ////excelPackage.Workbook.Worksheets[1].Cells["D9"].Value = Math.Round(ParseDoubleOrDefault(dict[cbFlowMeter.Text + ":ExtDiam.."]), 6).ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["G14"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "ZeroCut"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();

        //                    //excelPackage.Workbook.Worksheets[0].Cells["I20"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt1"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["I21"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt2"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["I22"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt3"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["I23"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt4"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["I24"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt5"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["J20"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr1"]),
        //                    //        4
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["J21"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr2"]),
        //                    //        4
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["J22"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr3"]),
        //                    //        4
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["J23"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr4"]),
        //                    //        4
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[0].Cells["J24"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr5"]),
        //                    //        4
        //                    //    )
        //                    //    .ToString();
        //                }
        //                catch (Exception ex)
        //                {
        //                    lbMessage.ForeColor = Color.Red;
        //                    lbMessage.Text = ex.Message;
        //                }

        //                // Worksheets[2]: sheet["SOS"]
        //                excelPackage.Workbook.Worksheets[2].Cells["B2"].Value = Text25.Text; //press
        //                excelPackage.Workbook.Worksheets[2].Cells["B3"].Value = Text24.Text; //temp

        //                excelPackage.Workbook.Worksheets[2].Cells["B19"].Value = txtCalSOS.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B18"].Value = txtFCSOS.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B20"].Value = txtDevSOS.Text;

        //                for (var i = 0; i < _reportData.Count; i++)
        //                {
        //                    // Worksheets[3]: sheet["Raw Data"]
        //                    var rowNo = i + 2;
        //                    excelPackage.Workbook.Worksheets[3].Cells["A" + rowNo].Value =
        //                        reportTime.ToString("MM/dd/yyyy");
        //                    excelPackage.Workbook.Worksheets[3].Cells["B" + rowNo].Value =
        //                        _reportData[i]["LocalTimeCol"];

        //                    //excelPackage.Workbook.Worksheets[3].Cells["C" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Qmeter"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["D" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Qflow"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["E" + rowNo].Value = isSystemStatusGood;

        //                    excelPackage.Workbook.Worksheets[3].Cells["F" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1Velocity"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["G" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2Velocity"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["H" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3Velocity"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["I" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4Velocity"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["J" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5Velocity"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["K" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6Velocity"]), 2);

        //                    //excelPackage.Workbook.Worksheets[3].Cells["L" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "AvgFlow"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["M" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1SpeedofSound"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["N" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2SpeedofSound"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["O" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3SpeedofSound"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["P" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4SpeedofSound"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["Q" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5SpeedofSound"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["R" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6SpeedofSound"]),
        //                            2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["S" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "AvgSndVel"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["T" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1Fault"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["U" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2Fault"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["V" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3Fault"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["W" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4Fault"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["X" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5Fault"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["Y" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6Fault"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["Z" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1ValidSamplePercent"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AA" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2ValidSamplePercent"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AB" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3ValidSamplePercent"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AC" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4ValidSamplePercent"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AD" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5ValidSamplePercent"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AE" + rowNo].Value =
        //                        Math.Round(
        //                            ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6ValidSamplePercent"]),
        //                            2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AF" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD1"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AG" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD2"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AH" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD1"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AI" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD2"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AJ" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD1"]), 2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["AK" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD2"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AL" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AM" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1d2AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AN" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AO" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2d2AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AP" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AQ" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3d2AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AR" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AS" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4d2AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AT" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AU" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5d2AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AV" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6d1AGCLevel"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AW" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6d2AGCLevel"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AX" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AY" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P1d2Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AZ" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BA" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P2d2Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BB" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BC" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P3d2Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BD" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BE" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P4d2Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BF" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BG" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P5d2Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BH" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6d1Snr"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BI" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "P6d2Snr"]), 2);

        //                    //excelPackage.Workbook.Worksheets[3].Cells["AV" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "LinearMtrFctr"]),
        //                    //        2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["BR" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "ProfileFactor"]),
        //                            4);

        //                    excelPackage.Workbook.Worksheets[3].Cells["CB" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Symmetry"]), 4);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["CC" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "CrossFlow"]), 4);


        //                    //excelPackage.Workbook.Worksheets[3].Cells["BE" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceA"]),
        //                    //        2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["BF" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceB"]),
        //                    //        2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["BG" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceC"]),
        //                    //        2);
        //                    //excelPackage.Workbook.Worksheets[3].Cells["BH" + rowNo].Value =
        //                    //    Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceD"]),
        //                    //        2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CK" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TotVolumeD1_L"]),
        //                            4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CL" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TotVolErrD1_L"]),
        //                            4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CM" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TotVolumeD2_L"]),
        //                            4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CN" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TotVolErrD2_L"]),
        //                            4);

        //                    excelPackage.Workbook.Worksheets[3].Cells["CO" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SwirlAnglePlane1"]),
        //                            4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CP" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SwirlAnglePlane2"]),
        //                            4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["CQ" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SwirlAnglePlane3"]),
        //                            4);
        //                }


        //                for (var j = 0; j < 4; j++)
        //                {
        //                    excelPackage
        //                        .Workbook
        //                        .Worksheets[j].ProtectedRanges.Add(
        //                            "protected",
        //                            new ExcelAddress(1, 1, 150, 150)
        //                        );
        //                    excelPackage.Workbook.Worksheets[j].Protection.SetPassword("Admin123");
        //                    excelPackage.Workbook.Worksheets[j].Protection.IsProtected = true;
        //                    excelPackage.Workbook.Worksheets[j].Protection.AllowEditObject = false;
        //                    //excelPackage.Workbook.Worksheets[1].Protection.AllowSelectLockedCells = false;
        //                    excelPackage.Workbook.Worksheets[j]
        //                        .Protection
        //                        .AllowEditScenarios = false;
        //                    excelPackage.Workbook.Worksheets[j].Protection.AllowSelectUnlockedCells = false;
        //                }

        //                excelPackage.Save();
        //            }
        //        }
        //        else
        //        {
        //            var templateDailyReport = new FileInfo(excelTemplatePath);

        //            using (var excelPackage = new ExcelPackage(newFile, templateDailyReport))
        //            {
        //                excelPackage.Workbook.Worksheets[2].Cells["B5"].Value = textBox0.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B6"].Value = textBox1.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B7"].Value = textBox2.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B8"].Value = textBox3.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B9"].Value = textBox4.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B10"].Value = textBox5.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B11"].Value = textBox6.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B12"].Value = Text2.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B13"].Value = textBox7.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B14"].Value = textBox12.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B15"].Value = textBox13.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D24"].Value = textBox14.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D25"].Value = textBox15.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D26"].Value = textBox17.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D27"].Value = textBox18.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D28"].Value = textBox19.Text;
        //                //excelPackage.Workbook.Worksheets[2].Cells["D29"].Value = textBox20.Text;

        //                excelPackage.Workbook.Worksheets[0].Cells["B1"].Value = cbStation.Text;
        //                excelPackage.Workbook.Worksheets[0].Cells["B2"].Value = cbFlowMeter.Text;
        //                excelPackage.Workbook.Worksheets[0].Cells["B7"].Value = ""; // SN
        //                excelPackage.Workbook.Worksheets[0].Cells["K1"].Value = DateTime.Now.ToLongDateString();
        //                excelPackage.Workbook.Worksheets[0].Cells["K2"].Value = DateTime.Now.ToLongTimeString();

        //                string descCalMethod = float.Parse(resultFT[streamTablePrefix + "CalMethod"]) switch
        //                {
        //                    0.0f => "None",
        //                    1.0f => "Polynomial",
        //                    2.0f => "Piece-wise linear",
        //                    _ => "None"
        //                };
        //                excelPackage.Workbook.Worksheets[0].Cells["G38"].Value = descCalMethod;

        //                excelPackage.Workbook.Worksheets[0].Cells["B59"].Value =
        //                    resultFT[streamTablePrefix + "QMeterValidity"];


        //                //excelPackage.Workbook.Worksheets[0].Cells["D5"].Value = reportTime.ToString(
        //                //    "yyyy-MM-dd HH:mm:ss"
        //                //);

        //                try
        //                {
        //                    //excelPackage.Workbook.Worksheets[1].Cells["B9"].Value = Math.Round(
        //                    //        ParseDoubleOrDefault(resultFT[streamTablePrefix + "USM_AvgFlow"]),
        //                    //        2
        //                    //    )
        //                    //    .ToString();
        //                    //excelPackage.Workbook.Worksheets[1].Cells["B10"].Value = txtFCSOS.Text;

        //                    excelPackage.Workbook.Worksheets[0].Cells["B8"].Value = Math.Round(intDiam, 6).ToString();
        //                    //excelPackage.Workbook.Worksheets[1].Cells["D9"].Value = Math.Round(ParseDoubleOrDefault(dict[cbFlowMeter.Text + ":ExtDiam.."]), 6).ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["G14"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "ZeroCut"]),
        //                            2
        //                        )
        //                        .ToString();

        //                    excelPackage.Workbook.Worksheets[0].Cells["I20"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt1"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I21"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt2"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I22"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt3"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I23"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt4"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I24"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt5"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I25"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt6"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["I26"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdFlwRt7"]),
        //                            2
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J20"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr1"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J21"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr2"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J22"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr3"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J23"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr4"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J24"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr5"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J25"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr6"]),
        //                            4
        //                        )
        //                        .ToString();
        //                    excelPackage.Workbook.Worksheets[0].Cells["J26"].Value = Math.Round(
        //                            ParseDoubleOrDefault(resultFT[streamTablePrefix + "FwdMtrFctr7"]),
        //                            4
        //                        )
        //                        .ToString();
        //                }
        //                catch (Exception ex)
        //                {
        //                    lbMessage.ForeColor = Color.Red;
        //                    lbMessage.Text = ex.Message;
        //                }

        //                // Worksheets[2]: sheet["SOS"]
        //                excelPackage.Workbook.Worksheets[2].Cells["B2"].Value = Text25.Text; //press
        //                excelPackage.Workbook.Worksheets[2].Cells["B3"].Value = Text24.Text; //temp

        //                excelPackage.Workbook.Worksheets[2].Cells["B19"].Value = txtCalSOS.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B18"].Value = txtFCSOS.Text;
        //                excelPackage.Workbook.Worksheets[2].Cells["B20"].Value = txtDevSOS.Text;

        //                for (var i = 0; i < _reportData.Count; i++)
        //                {
        //                    // Worksheets[3]: sheet["Raw Data"]
        //                    var rowNo = i + 2;
        //                    excelPackage.Workbook.Worksheets[3].Cells["A" + rowNo].Value =
        //                        reportTime.ToString("MM/dd/yyyy");
        //                    excelPackage.Workbook.Worksheets[3].Cells["B" + rowNo].Value =
        //                        _reportData[i]["LocalTimeCol"];

        //                    excelPackage.Workbook.Worksheets[3].Cells["C" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Qmeter"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["D" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Qflow"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["E" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SystemStatus"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["F" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelA"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["G" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelB"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["H" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelC"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["I" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "FlowVelD"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["J" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "AvgFlow"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["K" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SndVelA"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["L" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SndVelB"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["M" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SndVelC"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["N" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SndVelD"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["O" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "AvgSndVel"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["P" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "StatusA"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["Q" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "StatusB"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["R" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "StatusC"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["S" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "StatusD"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["T" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodA1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["U" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodA2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["V" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodB1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["W" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodB2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["X" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodC1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["Y" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodC2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["Z" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AA" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "PctGoodD2"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AB" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainA1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AC" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainA2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AD" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainB1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AE" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainB2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AF" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainC1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AG" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainC2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AH" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainD1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AI" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "GainD2"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AJ" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRA1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AK" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRA2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AL" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRB1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AM" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRB2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AN" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRC1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AO" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRC2"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AP" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRD1"]), 2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["AQ" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SNRD2"]), 2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AV" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "LinearMtrFctr"]),
        //                            2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["AX" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "ProfileFactor"]),
        //                            4);

        //                    excelPackage.Workbook.Worksheets[3].Cells["BC" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "Symmetry"]), 4);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BD" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "CrossFlow"]), 4);


        //                    excelPackage.Workbook.Worksheets[3].Cells["BE" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceA"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BF" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceB"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BG" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceC"]),
        //                            2);
        //                    excelPackage.Workbook.Worksheets[3].Cells["BH" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "TurbulenceD"]),
        //                            2);

        //                    excelPackage.Workbook.Worksheets[3].Cells["BI" + rowNo].Value =
        //                        Math.Round(ParseDoubleOrDefault(_reportData[i][streamTablePrefix + "SwirlAngle"]),
        //                            2);
        //                }


        //                for (var j = 0; j < 4; j++)
        //                {
        //                    excelPackage
        //                        .Workbook
        //                        .Worksheets[j].ProtectedRanges.Add(
        //                            "protected",
        //                            new ExcelAddress(1, 1, 150, 150)
        //                        );
        //                    excelPackage.Workbook.Worksheets[j].Protection.SetPassword("Admin123");
        //                    excelPackage.Workbook.Worksheets[j].Protection.IsProtected = true;
        //                    excelPackage.Workbook.Worksheets[j].Protection.AllowEditObject = false;
        //                    //excelPackage.Workbook.Worksheets[1].Protection.AllowSelectLockedCells = false;
        //                    excelPackage.Workbook.Worksheets[j]
        //                        .Protection
        //                        .AllowEditScenarios = false;
        //                    excelPackage.Workbook.Worksheets[j].Protection.AllowSelectUnlockedCells = false;
        //                }

        //                excelPackage.Save();
        //            }
        //        }
        //    }

        //    cmdGenerateReport.Enabled = true;
        //    lbMessage.ForeColor = Color.Green;
        //    lbMessage.Text = "Report generated.";
        //}
        private void GenerateSOSCheckReport()
        {
            // --- 准备用于错误日志的上下文变量 ---
            string newReportName = null; // 用于记录当前操作的文件名
            string currentKey = "N/A"; // 用于记录最近一次尝试访问的字典的Key
            int currentRowIndex = -1; // 用于记录处理到_reportData的第几行时出错

            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                _excelFilePathToOpen = "";

                newReportName =
                    "GUSM_SOSCHECK_"
                    + cbFlowMeter.Text
                    + "_"
                    + reportTime.ToString("yyyy-MM-dd HH-mm-ss")
                    + ".xlsx";
                var savedPath = @"C:\Reports\" + cbStation.Text.Trim() + @"\" + cbFlowMeter.Text.Trim() +
                                @"\SOSCheckReport\";

                if (!Directory.Exists(savedPath))
                {
                    Directory.CreateDirectory(savedPath);
                }

                var newFile = new FileInfo(Path.Combine(savedPath, newReportName));
                _excelFilePathToOpen = newFile.FullName;

                // 根据设备类型选择不同的模板进行操作
                if (isRMGDevice)
                {
                    var templateDailyReport = new FileInfo(excelTemplatePathRMG);

                    using (var excelPackage = new ExcelPackage(newFile, templateDailyReport))
                    {
                        // 填充简单数据
                        excelPackage.Workbook.Worksheets[2].Cells["B5"].Value = textBox0.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B6"].Value = textBox1.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B7"].Value = textBox2.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B8"].Value = textBox3.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B9"].Value = textBox4.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B10"].Value = textBox5.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B11"].Value = textBox6.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B12"].Value = Text2.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B13"].Value = textBox7.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B14"].Value = textBox12.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B15"].Value = textBox13.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B1"].Value = cbStation.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B2"].Value = cbFlowMeter.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B7"].Value = ""; // SN
                        excelPackage.Workbook.Worksheets[0].Cells["K1"].Value = DateTime.Now.ToLongDateString();
                        excelPackage.Workbook.Worksheets[0].Cells["K2"].Value = DateTime.Now.ToLongTimeString();
                        excelPackage.Workbook.Worksheets[2].Cells["B2"].Value = Text25.Text; //press
                        excelPackage.Workbook.Worksheets[2].Cells["B3"].Value = Text24.Text; //temp
                        excelPackage.Workbook.Worksheets[2].Cells["B19"].Value = txtCalSOS.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B18"].Value = txtFCSOS.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B20"].Value = txtDevSOS.Text;

                        // --- 开始处理循环数据 ---
                        for (var i = 0; i < _reportData.Count; i++)
                        {
                            currentRowIndex = i; // 记录当前处理的行号
                            var rowNo = i + 2;

                            excelPackage.Workbook.Worksheets[3].Cells["A" + rowNo].Value =
                                reportTime.ToString("MM/dd/yyyy");

                            currentKey = "LocalTimeCol";
                            excelPackage.Workbook.Worksheets[3].Cells["B" + rowNo].Value = _reportData[i][currentKey];
                            excelPackage.Workbook.Worksheets[3].Cells["E" + rowNo].Value = isSystemStatusGood;

                            // Velocities
                            currentKey = streamTablePrefix + "P1Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["F" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["G" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["H" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["I" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["J" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6Velocity";
                            excelPackage.Workbook.Worksheets[3].Cells["K" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // Speeds of Sound
                            currentKey = streamTablePrefix + "P1SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["M" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["N" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["O" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["P" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["Q" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6SpeedofSound";
                            excelPackage.Workbook.Worksheets[3].Cells["R" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // Faults
                            currentKey = streamTablePrefix + "P1Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["T" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["U" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["V" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["W" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["X" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6Fault";
                            excelPackage.Workbook.Worksheets[3].Cells["Y" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // Valid Sample Percentages
                            currentKey = streamTablePrefix + "P1ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["Z" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["AA" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["AB" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["AC" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["AD" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6ValidSamplePercent";
                            excelPackage.Workbook.Worksheets[3].Cells["AE" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // AGC Levels
                            currentKey = streamTablePrefix + "P1d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AL" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P1d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AM" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AN" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AO" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AP" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AQ" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AR" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AS" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AT" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AU" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6d1AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AV" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6d2AGCLevel";
                            excelPackage.Workbook.Worksheets[3].Cells["AW" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // SNRs
                            currentKey = streamTablePrefix + "P1d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["AX" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P1d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["AY" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["AZ" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P2d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BA" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BB" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P3d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BC" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BD" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P4d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BE" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BF" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P5d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BG" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6d1Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BH" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "P6d2Snr";
                            excelPackage.Workbook.Worksheets[3].Cells["BI" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            // Other parameters
                            currentKey = streamTablePrefix + "ProfileFactor";
                            excelPackage.Workbook.Worksheets[3].Cells["BR" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "Symmetry";
                            excelPackage.Workbook.Worksheets[3].Cells["CB" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "TotVolumeD1_L";
                            excelPackage.Workbook.Worksheets[3].Cells["CK" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "TotVolErrD1_L";
                            excelPackage.Workbook.Worksheets[3].Cells["CL" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "TotVolumeD2_L";
                            excelPackage.Workbook.Worksheets[3].Cells["CM" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "TotVolErrD2_L";
                            excelPackage.Workbook.Worksheets[3].Cells["CN" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "SwirlAnglePlane1";
                            excelPackage.Workbook.Worksheets[3].Cells["CO" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "SwirlAnglePlane2";
                            excelPackage.Workbook.Worksheets[3].Cells["CP" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "SwirlAnglePlane3";
                            excelPackage.Workbook.Worksheets[3].Cells["CQ" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                        }

                        // 循环结束后重置行号
                        currentRowIndex = -1;

                        // 保护工作表
                        for (var j = 0; j < 4; j++)
                        {
                            excelPackage.Workbook.Worksheets[j].ProtectedRanges
                                .Add("protected", new ExcelAddress(1, 1, 150, 150));
                            excelPackage.Workbook.Worksheets[j].Protection.SetPassword("Admin123");
                            excelPackage.Workbook.Worksheets[j].Protection.IsProtected = true;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowEditObject = false;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowEditScenarios = false;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowSelectUnlockedCells = false;
                        }

                        excelPackage.Save();
                    }
                }
                else // 非 RMG 设备
                {
                    var templateDailyReport = new FileInfo(excelTemplatePath);

                    using (var excelPackage = new ExcelPackage(newFile, templateDailyReport))
                    {
                        // 填充简单数据
                        excelPackage.Workbook.Worksheets[2].Cells["B5"].Value = textBox0.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B6"].Value = textBox1.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B7"].Value = textBox2.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B8"].Value = textBox3.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B9"].Value = textBox4.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B10"].Value = textBox5.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B11"].Value = textBox6.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B12"].Value = Text2.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B13"].Value = textBox7.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B14"].Value = textBox12.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B15"].Value = textBox13.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B1"].Value = cbStation.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B2"].Value = cbFlowMeter.Text;
                        excelPackage.Workbook.Worksheets[0].Cells["B7"].Value = ""; // SN
                        excelPackage.Workbook.Worksheets[0].Cells["K1"].Value = DateTime.Now.ToLongDateString();
                        excelPackage.Workbook.Worksheets[0].Cells["K2"].Value = DateTime.Now.ToLongTimeString();
                        excelPackage.Workbook.Worksheets[2].Cells["B2"].Value = Text25.Text; //press
                        excelPackage.Workbook.Worksheets[2].Cells["B3"].Value = Text24.Text; //temp
                        excelPackage.Workbook.Worksheets[2].Cells["B19"].Value = txtCalSOS.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B18"].Value = txtFCSOS.Text;
                        excelPackage.Workbook.Worksheets[2].Cells["B20"].Value = txtDevSOS.Text;

                        // --- 访问 resultFT 字典 ---
                        currentKey = streamTablePrefix + "CalMethod";
                        string descCalMethod = float.Parse(resultFT[currentKey]) switch
                        {
                            0.0f => "None",
                            1.0f => "Polynomial",
                            2.0f => "Piece-wise linear",
                            _ => "None"
                        };
                        excelPackage.Workbook.Worksheets[0].Cells["G38"].Value = descCalMethod;

                        currentKey = streamTablePrefix + "QMeterValidity";
                        excelPackage.Workbook.Worksheets[0].Cells["B59"].Value = resultFT[currentKey];

                        excelPackage.Workbook.Worksheets[0].Cells["B8"].Value = Math.Round(intDiam, 6).ToString();

                        currentKey = streamTablePrefix + "ZeroCut";
                        excelPackage.Workbook.Worksheets[0].Cells["G14"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();

                        // FwdFlwRt
                        currentKey = streamTablePrefix + "FwdFlwRt1";
                        excelPackage.Workbook.Worksheets[0].Cells["I20"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt2";
                        excelPackage.Workbook.Worksheets[0].Cells["I21"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt3";
                        excelPackage.Workbook.Worksheets[0].Cells["I22"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt4";
                        excelPackage.Workbook.Worksheets[0].Cells["I23"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt5";
                        excelPackage.Workbook.Worksheets[0].Cells["I24"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt6";
                        excelPackage.Workbook.Worksheets[0].Cells["I25"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt7";
                        excelPackage.Workbook.Worksheets[0].Cells["I26"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt8";
                        excelPackage.Workbook.Worksheets[0].Cells["I27"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt9";
                        excelPackage.Workbook.Worksheets[0].Cells["I28"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt10";
                        excelPackage.Workbook.Worksheets[0].Cells["I29"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt11";
                        excelPackage.Workbook.Worksheets[0].Cells["I30"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();
                        currentKey = streamTablePrefix + "FwdFlwRt12";
                        excelPackage.Workbook.Worksheets[0].Cells["I31"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 2).ToString();


                        // FwdMtrFctr
                        currentKey = streamTablePrefix + "FwdMtrFctr1";
                        excelPackage.Workbook.Worksheets[0].Cells["J20"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr2";
                        excelPackage.Workbook.Worksheets[0].Cells["J21"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr3";
                        excelPackage.Workbook.Worksheets[0].Cells["J22"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr4";
                        excelPackage.Workbook.Worksheets[0].Cells["J23"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr5";
                        excelPackage.Workbook.Worksheets[0].Cells["J24"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr6";
                        excelPackage.Workbook.Worksheets[0].Cells["J25"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr7";
                        excelPackage.Workbook.Worksheets[0].Cells["J26"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr8";
                        excelPackage.Workbook.Worksheets[0].Cells["J27"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr9";
                        excelPackage.Workbook.Worksheets[0].Cells["J28"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr10";
                        excelPackage.Workbook.Worksheets[0].Cells["J29"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr11";
                        excelPackage.Workbook.Worksheets[0].Cells["J30"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();
                        currentKey = streamTablePrefix + "FwdMtrFctr12";
                        excelPackage.Workbook.Worksheets[0].Cells["J31"].Value =
                            Math.Round(ParseDoubleOrDefault(resultFT[currentKey]), 4).ToString();


                        // --- 开始处理循环数据 ---
                        for (var i = 0; i < _reportData.Count; i++)
                        {
                            currentRowIndex = i; // 记录行号
                            var rowNo = i + 2;

                            excelPackage.Workbook.Worksheets[3].Cells["A" + rowNo].Value =
                                reportTime.ToString("MM/dd/yyyy");

                            currentKey = "LocalTimeCol";
                            excelPackage.Workbook.Worksheets[3].Cells["B" + rowNo].Value = _reportData[i][currentKey];

                            currentKey = streamTablePrefix + "Qmeter";
                            excelPackage.Workbook.Worksheets[3].Cells["C" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "Qflow";
                            excelPackage.Workbook.Worksheets[3].Cells["D" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SystemStatus";
                            excelPackage.Workbook.Worksheets[3].Cells["E" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "FlowVelA";
                            excelPackage.Workbook.Worksheets[3].Cells["F" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "FlowVelB";
                            excelPackage.Workbook.Worksheets[3].Cells["G" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "FlowVelC";
                            excelPackage.Workbook.Worksheets[3].Cells["H" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "FlowVelD";
                            excelPackage.Workbook.Worksheets[3].Cells["I" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "AvgFlow";
                            excelPackage.Workbook.Worksheets[3].Cells["J" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "SndVelA";
                            excelPackage.Workbook.Worksheets[3].Cells["K" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SndVelB";
                            excelPackage.Workbook.Worksheets[3].Cells["L" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SndVelC";
                            excelPackage.Workbook.Worksheets[3].Cells["M" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SndVelD";
                            excelPackage.Workbook.Worksheets[3].Cells["N" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "AvgSndVel";
                            excelPackage.Workbook.Worksheets[3].Cells["O" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "StatusA";
                            excelPackage.Workbook.Worksheets[3].Cells["P" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "StatusB";
                            excelPackage.Workbook.Worksheets[3].Cells["Q" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "StatusC";
                            excelPackage.Workbook.Worksheets[3].Cells["R" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "StatusD";
                            excelPackage.Workbook.Worksheets[3].Cells["S" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "PctGoodA1";
                            excelPackage.Workbook.Worksheets[3].Cells["T" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodA2";
                            excelPackage.Workbook.Worksheets[3].Cells["U" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodB1";
                            excelPackage.Workbook.Worksheets[3].Cells["V" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodB2";
                            excelPackage.Workbook.Worksheets[3].Cells["W" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodC1";
                            excelPackage.Workbook.Worksheets[3].Cells["X" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodC2";
                            excelPackage.Workbook.Worksheets[3].Cells["Y" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodD1";
                            excelPackage.Workbook.Worksheets[3].Cells["Z" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "PctGoodD2";
                            excelPackage.Workbook.Worksheets[3].Cells["AA" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "GainA1";
                            excelPackage.Workbook.Worksheets[3].Cells["AB" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainA2";
                            excelPackage.Workbook.Worksheets[3].Cells["AC" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainB1";
                            excelPackage.Workbook.Worksheets[3].Cells["AD" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainB2";
                            excelPackage.Workbook.Worksheets[3].Cells["AE" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainC1";
                            excelPackage.Workbook.Worksheets[3].Cells["AF" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainC2";
                            excelPackage.Workbook.Worksheets[3].Cells["AG" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainD1";
                            excelPackage.Workbook.Worksheets[3].Cells["AH" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "GainD2";
                            excelPackage.Workbook.Worksheets[3].Cells["AI" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "SNRA1";
                            excelPackage.Workbook.Worksheets[3].Cells["AJ" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRA2";
                            excelPackage.Workbook.Worksheets[3].Cells["AK" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRB1";
                            excelPackage.Workbook.Worksheets[3].Cells["AL" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRB2";
                            excelPackage.Workbook.Worksheets[3].Cells["AM" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRC1";
                            excelPackage.Workbook.Worksheets[3].Cells["AN" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRC2";
                            excelPackage.Workbook.Worksheets[3].Cells["AO" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRD1";
                            excelPackage.Workbook.Worksheets[3].Cells["AP" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SNRD2";
                            excelPackage.Workbook.Worksheets[3].Cells["AQ" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);

                            currentKey = streamTablePrefix + "LinearMtrFctr";
                            excelPackage.Workbook.Worksheets[3].Cells["AV" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "ProfileFactor";
                            excelPackage.Workbook.Worksheets[3].Cells["AX" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "Symmetry";
                            excelPackage.Workbook.Worksheets[3].Cells["BC" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "CrossFlow";
                            excelPackage.Workbook.Worksheets[3].Cells["BD" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 4);
                            currentKey = streamTablePrefix + "TurbulenceA";
                            excelPackage.Workbook.Worksheets[3].Cells["BE" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "TurbulenceB";
                            excelPackage.Workbook.Worksheets[3].Cells["BF" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "TurbulenceC";
                            excelPackage.Workbook.Worksheets[3].Cells["BG" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "TurbulenceD";
                            excelPackage.Workbook.Worksheets[3].Cells["BH" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                            currentKey = streamTablePrefix + "SwirlAngle";
                            excelPackage.Workbook.Worksheets[3].Cells["BI" + rowNo].Value =
                                Math.Round(ParseDoubleOrDefault(_reportData[i][currentKey]), 2);
                        }

                        // 循环结束后重置行号
                        currentRowIndex = -1;

                        // 保护工作表
                        for (var j = 0; j < 4; j++)
                        {
                            excelPackage.Workbook.Worksheets[j].ProtectedRanges
                                .Add("protected", new ExcelAddress(1, 1, 150, 150));
                            excelPackage.Workbook.Worksheets[j].Protection.SetPassword("Admin123");
                            excelPackage.Workbook.Worksheets[j].Protection.IsProtected = true;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowEditObject = false;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowEditScenarios = false;
                            excelPackage.Workbook.Worksheets[j].Protection.AllowSelectUnlockedCells = false;
                        }

                        excelPackage.Save();
                    }
                }

                // --- 如果所有代码都成功执行，则显示成功消息 ---
                cmdGenerateReport.Enabled = true;
                lbMessage.ForeColor = Color.Green;
                lbMessage.Text = "Report generated.";
            }
            catch (Exception ex)
            {
                // --- 发生任何错误时，执行这里的代码 ---

                // 1. 构建详细的自定义错误消息
                string detailedErrorMessage = $"生成报告 '{newReportName ?? "unknown"}' 时失败。";
                if (currentRowIndex != -1)
                {
                    detailedErrorMessage += $" 错误发生在处理第 {currentRowIndex + 1} 行数据时。";
                }

                detailedErrorMessage += $" 最近尝试访问的键(Key)是: '{currentKey}'。";
                if (ex is KeyNotFoundException)
                {
                    detailedErrorMessage += " (这是一个'键未找到'错误，请检查该键是否存在于数据源中)。";
                }

                // 2. 调用 LogHelper 记录日志
                LogHelper.LogError(ex, detailedErrorMessage);

                // 3. 在界面上给用户清晰的反馈
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "生成报告失败! 详情已记录到日志文件。";
                MessageBox.Show(
                    "抱歉，生成报告时发生了一个意外错误。\n\n详细信息已经记录到程序目录下的 error_log.txt 文件中，请将此文件提供给技术支持人员。",
                    "操作失败",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );

                // 4. 确保UI可以继续使用
                cmdGenerateReport.Enabled = true;
            }
        }

        public async void cmdStart_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(tbCurrentStreamNo.Text))
            {
                streamFcTableName = cbFlowComputer.Text;
                streamFtTableName = cbFlowMeter.Text;
            }
            else
            {
                MessageBox.Show("Please select the Meter and Flow Computer.");
                return;
            }

            progressBar1.Visible = true;
            progressBar1.Style = ProgressBarStyle.Marquee;
            progressBar1.MarqueeAnimationSpeed = 30;

            ResetPanels(); // 重置所有Panel的状态
            ResetFieldTypeStatuses();
            DebugPrintFieldTypeStatuses();
            lbReadData.Text = "Reading...";

            cmdStart.Enabled = false;
            cbFlowMeter.Enabled = false;
            cbTime.Enabled = false;

            var dateTime = DateTime.Now;
            //lbDate.Text = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
            reportTime = dateTime;

            // 新增：查询设备厂商并设置字段映射
            currentDeviceManufacturer = await _stationManager.GetDeviceManufacturerAsync(streamFtTableName);
            isRMGDevice = currentDeviceManufacturer.Equals("RMG", StringComparison.OrdinalIgnoreCase);
            InitializeFieldMappings();


            try
            {
                //var targetTime = new DateTime(2025, 6, 21, 17, 3, 30);
                var targetTime = DateTime.Now;
                lbEndTime.Text = targetTime.ToString("yyyy-MM-dd HH:mm:ss");
                lbStartTime.Text = targetTime.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss");
                var data = await ReadDataInTimeRangeAsync(ConnectionString, targetTime, 10, streamFcTableName);

                // 在UI线程上更新图表
                await Task.Run(() =>
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        formsPlot1.Plot.Clear();
                        formsPlot2.Plot.Clear();
                        formsPlot3.Plot.Clear();
                        dataPlotter.PlotData(formsPlot1, data, 0.1, "SLCT_METHANE" + historyTagSuffix);
                        formsPlot1.Refresh();
                        dataPlotter.PlotData(formsPlot2, data, 0.1, "PressInuse" + historyTagSuffix);
                        formsPlot2.Refresh();
                        dataPlotter.PlotData(formsPlot3, data, 0.1, "TempInuse" + historyTagSuffix);
                        formsPlot3.Refresh();
                    });
                });

                panelReadData.BackColor = Color.Green;
                lbReadData.ForeColor = Color.White;
                lbReadData.Text = "Data reading complete.";

                var totalFields = fieldTypeStatuses.Values.Sum(v => v.TotalFields);
                Debug.WriteLine($"Total fields to check: {totalFields}");
                var checkedFields = 0;

                //var rangeThresholds = new Dictionary<string, double>
                //{
                //    { "SLCT", 0.001 },
                //    { "PressInuse_Check", 500 },
                //    { "TempInuse_Check", 0.5 },
                //    { "USMAVGVOS_Check", 0.5 }
                //};

                //var stdDevThresholds = new Dictionary<string, double>
                //{
                //    { "SLCT", 0.03 },
                //    { "PressInuse_Check", 50 },
                //    { "TempInuse_Check", 0.1 },
                //    { "USMAVGVOS_Check", 0.15 }
                //};

                var checkResult = await CheckDataStabilityAsync(data, (field, isStable) =>
                {
                    UpdatePanelColor(field, isStable);
                    Interlocked.Increment(ref checkedFields);
                    Debug.WriteLine($"Checked fields: {checkedFields}/{totalFields}");
                }, fieldTypeStatuses, thresholdReader.RangeThresholds, thresholdReader.StdDevThresholds);

                Debug.WriteLine("CheckDataStabilityAsync completed");
                DebugPrintFieldTypeStatuses();

                // 等待所有UI更新完成
                await Task.Delay(500); // 给UI更新一些时间

                Invoke((MethodInvoker)delegate
                {
                    HandleFinalResult(checkResult);
                    Debug.WriteLine("HandleFinalResult completed");
                    DebugPrintFieldTypeStatuses();
                });


                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    var sqlFC = @"
                DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                SELECT TOP 1 *
                FROM {0}
                WHERE LocalTimeCol <= @InputDateTime
                ORDER BY LocalTimeCol DESC";

                    var sqlFT = @"
                DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                SELECT TOP 1 *
                FROM {0}
                WHERE LocalTimeCol <= @InputDateTime
                ORDER BY LocalTimeCol DESC";

                    // 替换表名
                    sqlFC = string.Format(sqlFC, streamFcTableName);
                    sqlFT = string.Format(sqlFT, streamFtTableName);

                    using (var command = new SqlCommand(sqlFC, connection))
                    {
                        command.Parameters.AddWithValue("@TargetTime", targetTime);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                for (var i = 0; i < reader.FieldCount; i++)
                                    result[reader.GetName(i)] = reader[i].ToString();
                            }
                            else
                            {
                                Log.Warn("Get the latest snapshot data of FC FAILED!");
                                return;
                            }
                        }
                    }

                    using (var command = new SqlCommand(sqlFT, connection))
                    {
                        command.Parameters.AddWithValue("@TargetTime", targetTime);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                for (var i = 0; i < reader.FieldCount; i++)
                                    resultFT[reader.GetName(i)] = reader[i].ToString();
                            }
                            else
                            {
                                Log.Warn("Get the latest snapshot data of USM FAILED!");
                                return;
                            }
                        }
                    }
                }

                // must change the source to FT history data table


                //panelStatusA.BackColor = ParseDoubleOrDefault(resultFT["StatusA"]) == 0
                //    ? Color.Green
                //    : Color.Red;
                //panelStatusB.BackColor = ParseDoubleOrDefault(resultFT["StatusB"]) == 0
                //    ? Color.Green
                //    : Color.Red;
                //panelStatusC.BackColor = ParseDoubleOrDefault(resultFT["StatusC"]) == 0
                //    ? Color.Green
                //    : Color.Red;
                //panelStatusD.BackColor = ParseDoubleOrDefault(resultFT["StatusD"]) == 0
                //    ? Color.Green
                //    : Color.Red;
                if (isRMGDevice)
                {
                    intDiam = 0.0;
                    GasVel = ParseDoubleOrDefault(result["USMAvgVOSInuse" + historyTagSuffix]);

                    // RMG设备：状态A/B/C/D映射为P1/P2/P3/P4Status
                    var statusP1 = GetMappedFieldValueSimple(resultFT, "StatusA"); // → P1Status
                    var statusP2 = GetMappedFieldValueSimple(resultFT, "StatusB"); // → P2Status
                    var statusP3 = GetMappedFieldValueSimple(resultFT, "StatusC"); // → P3Status
                    var statusP4 = GetMappedFieldValueSimple(resultFT, "StatusD"); // → P4Status

                    // RMG特有的P5和P6状态
                    var statusP5 = GetRMGSpecificFieldValueSimple(resultFT, "P5Fault");
                    var statusP6 = GetRMGSpecificFieldValueSimple(resultFT, "P6Fault");

                    // 设置面板颜色
                    panelStatusA.BackColor = statusP1 == 0 ? Color.Green : Color.Red;
                    panelStatusB.BackColor = statusP2 == 0 ? Color.Green : Color.Red;
                    panelStatusC.BackColor = statusP3 == 0 ? Color.Green : Color.Red;
                    panelStatusD.BackColor = statusP4 == 0 ? Color.Green : Color.Red;
                    panelStatusE.BackColor = statusP5 == 0 ? Color.Green : Color.Red;
                    panelStatusF.BackColor = statusP6 == 0 ? Color.Green : Color.Red;

                    isSystemStatusGood =
                        new[] { statusP1, statusP2, statusP3, statusP4, statusP5, statusP6 }.All(d => d == 0);
                    panelSystemStatus.BackColor = isSystemStatusGood
                        ? Color.Green
                        : Color.Red;

                    txtFCSOS.Text = Math.Round(ParseDoubleOrDefault(result["USMAvgVOSInuse" + historyTagSuffix]), 4)
                        .ToString();

                    //lbPressUnit.Text = "kPa"; // RMG设备使用kPa作为压力单位，全部转为Mpa显示
                    Text25.Text = Math.Round(ParseDoubleOrDefault(result["PressInuse" + historyTagSuffix]) / 1000, 3)
                        .ToString();
                }
                else
                {
                    // DANIEL GUSM
                    var statusA = GetMappedFieldValueSimple(resultFT, "StatusA");
                    var statusB = GetMappedFieldValueSimple(resultFT, "StatusB");
                    var statusC = GetMappedFieldValueSimple(resultFT, "StatusC");
                    var statusD = GetMappedFieldValueSimple(resultFT, "StatusD");
                    var systemStatus = GetMappedFieldValueSimple(resultFT, "SystemStatus");

                    intDiam = ParseDoubleOrDefault(result["SpoolIntDia" + historyTagSuffix]);
                    GasVel = ParseDoubleOrDefault(result["USMAvgVOS" + historyTagSuffix]);

                    panelStatusA.BackColor = statusA == 0 ? Color.Green : Color.Red;
                    panelStatusB.BackColor = statusB == 0 ? Color.Green : Color.Red;
                    panelStatusC.BackColor = statusC == 0 ? Color.Green : Color.Red;
                    panelStatusD.BackColor = statusD == 0 ? Color.Green : Color.Red;

                    panelSystemStatus.BackColor = systemStatus == 0 ? Color.Green : Color.Red;


                    txtFCSOS.Text = Math.Round(ParseDoubleOrDefault(result["USMAvgVOS" + historyTagSuffix]), 4)
                        .ToString();
                    //lbPressUnit.Text = "MPa"; // DANIEL设备使用MPa作为压力单位
                    Text25.Text = Math.Round(ParseDoubleOrDefault(result["PressInuse" + historyTagSuffix]), 3)
                        .ToString();
                }


                textBox0.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_METHANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox1.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_ETHANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox2.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_PROPANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox3.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_N_BUTANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox4.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_I_BUTANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox5.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_N_PENTANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox6.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_I_PENTANE" + historyTagSuffix]), 4)
                    .ToString();
                Text2.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_NEO_PENTANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox7.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_HEXANE" + historyTagSuffix]), 4)
                    .ToString();
                textBox12.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_NITROGEN" + historyTagSuffix]), 4)
                    .ToString();
                textBox13.Text = Math.Round(ParseDoubleOrDefault(result["SLCT_CO2" + historyTagSuffix]), 4)
                    .ToString();
                textBox16.Text = "0.0";
                Text24.Text = Math.Round(ParseDoubleOrDefault(result["TempInuse" + historyTagSuffix]), 3)
                    .ToString();


                CalTotComp();

                _reportData = await ReadDataInTimeRangeAsync(ConnectionString, targetTime, 10, streamFtTableName);


                cmdGenerateReport.Enabled = true;
            }
            catch (MethodException ex)
            {
                switch (ex.MethodName)
                {
                    case "ReadDataInTimeRange":
                        Log.Error("Error occurred in ReadDataInTimeRange: " + ex.Message);
                        panelReadData.BackColor = Color.Red;
                        lbReadData.Text = "Get data FAILED.";
                        break;
                    case "CheckDataStabilityAsync":
                        Log.Error("Error occurred in CheckDataStabilityAsync: " + ex.Message);
                        lbMessage.ForeColor = Color.Red;
                        lbMessage.Text = "Error: " + ex.Message;
                        break;
                    default:
                        Log.Error("Unknown error: " + ex.Message);
                        break;
                }
            }
            catch (Exception ex)
            {
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "Error: " + ex.Message;
            }
            finally
            {
                progressBar1.MarqueeAnimationSpeed = 0;
                progressBar1.Style = ProgressBarStyle.Blocks;
                progressBar1.Visible = false;
            }
        }

        private void ResetFieldTypeStatuses()
        {
            foreach (var status in fieldTypeStatuses.Values)
            {
                status.TotalFields = 0;
                status.CheckedFields = 0;
                status.IsAllStable = true;
            }

            Debug.WriteLine("FieldTypeStatuses reset");
            DebugPrintFieldTypeStatuses();
        }

        private void ResetPanels()
        {
            foreach (var (panel, label) in fieldPanels.Values)
                Invoke((MethodInvoker)delegate
                {
                    panel.BackColor = SystemColors.Control;
                    label.Text = "Waiting...";
                    label.ForeColor = Color.Black;
                });

            panelReadData.BackColor = SystemColors.Control;
            lbReadData.Text = "";
            lbReadData.ForeColor = Color.Black;

            panelStatusA.BackColor = Color.WhiteSmoke;
            panelStatusB.BackColor = Color.WhiteSmoke;
            panelStatusC.BackColor = Color.WhiteSmoke;
            panelStatusD.BackColor = Color.WhiteSmoke;
            panelStatusE.BackColor = Color.WhiteSmoke;
            panelStatusF.BackColor = Color.WhiteSmoke;
            panelSystemStatus.BackColor = Color.WhiteSmoke;
        }

        private void cmdClear_Click(object sender, EventArgs e)
        {
            Clear();
            ResetPanels(); // 重置所有Panel的状态

            ResetFieldTypeStatuses();
            formsPlot1.Plot.Clear();
            formsPlot1.Refresh();
            formsPlot2.Plot.Clear();
            formsPlot2.Refresh();
            formsPlot3.Plot.Clear();
            formsPlot3.Refresh();

            if (poller != null)
            {
                poller.Stop();
                poller.Dispose();
                poller = null;
            }

            txtDevSOS.BackColor = Color.LightGray;
            cmdStart.Enabled = true;
            cbFlowMeter.Enabled = true;
            cmdGenerateReport.Enabled = false;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            cmdGenerateReport.Enabled = false;

            if (count == 0)
            {
                timer1.Stop();
                lblWaitTime.Text = "Data reading completed.";
                cmdGenerateReport.Enabled = true;
            }
            else
            {
                count--;
                lblWaitTime.Text = count.ToString();
            }
        }

        private void timer2_Tick(object sender, EventArgs e)
        {
            //if (generateExcelTrigger)
            //{
            //    GenerateSOSCheckReport();
            //    generateExcelTrigger = false;
            //    timer2.Stop();
            //    cbTime.Enabled = true;
            //}
        }

        private void cmdGenerateReport_Click(object sender, EventArgs e)
        {
            bool isSussess = float.TryParse(txtCalSOS.Text, out float calSOS);
            if (string.IsNullOrEmpty(txtCalSOS.Text) || calSOS == 0)
            {
                MessageBox.Show("Please calculate the SOS first.");
                return;
            }

            cmdGenerateReport.Enabled = false;
            //cbTime.Enabled = false;

            GenerateSOSCheckReport();

            DialogResult result = MessageBox.Show(
                "Report is ready, open now?",
                "Report",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                try
                {
                    // 检查文件是否存在
                    if (File.Exists(_excelFilePathToOpen))
                    {
                        // 打开 Excel 文件
                        ProcessStartInfo startInfo = new ProcessStartInfo
                        {
                            FileName = _excelFilePathToOpen,
                            UseShellExecute = true
                        };
                        Process.Start(startInfo);
                    }
                    else
                    {
                        MessageBox.Show("The specified Excel file does not exist.", "FileNotFound",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"An error occurred while opening the Excel file.：\n{ex.Message}", "Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            //backgroundWorker1.WorkerReportsProgress = true;
            //backgroundWorker1.DoWork += backgroundWorker1_DoWork;
            //backgroundWorker1.RunWorkerAsync();
            //timer2.Start();
        }

        private void cbTime_SelectedIndexChanged(object sender, EventArgs e)
        {
            dataCollectionDurationMiniutes = int.Parse(cbTime.SelectedItem.ToString());
            count = dataCollectionDurationMiniutes * 60;
        }

        private string StreanNoToSN(string streamNo)
        {
            switch (streamNo)
            {
                case "01A":
                    return "150063351";

                case "01B":
                    return "150063352";

                case "01C":
                    return "150063353";

                case "01D":
                    return "160115094";

                case "01E":
                    return "160115459";

                case "01F":
                    return "160115093";

                case "01G":
                    return "160117664";

                case "02A":
                    return "15381510";

                case "02B":
                    return "150057172";

                case "0102":
                    return "15089678";

                case "0112":
                    return "15383996";

                default:
                    return "";
            }
        }

        private int SOSCalculate_Auto(DateTime dateTime, string streamNo, string oeTableName)
        {
            reportTime = dateTime;
            var calSOS = 0.0;
            //bool PfCheckResult;
            //bool TfCheckResult;
            //bool VelCheckResult;

            try
            {
                var dt1 = GetRawDataFromOE(dateTime, oeTableName);

                if (dt1.Rows.Count == 0)
                {
                    Log.Info(
                        "SOSCalculate_Auto("
                        + dateTime
                        + ","
                        + streamNo
                        + ","
                        + oeTableName
                        + "):"
                        + "数据库无对应数据"
                    );
                    return -1;
                }

                for (var i = 0; i < dt1.Rows.Count; i++)
                    result.Add(dt1.Rows[i]["name"].ToString(), dt1.Rows[i]["value"].ToString());

                intDiam = ParseDoubleOrDefault(result[streamNo + ":IntDiam.."]);
                extDiam = ParseDoubleOrDefault(result[streamNo + ":ExtDiam.."]);
                //tempCoeff = ParseDoubleOrDefault(dict[streamNo + ":TempCoeff.."]);

                //YoungModulus = ParseDoubleOrDefault(dict[streamNo + ":YoungModulus.."]);
                GasVel = ParseDoubleOrDefault(result[streamNo + ":AverageVel.."]);

                var AvgSndVel = ParseDoubleOrDefault(result[streamNo + ":AvgSndVel.."]);

                var A10 = new AGA10STRUCT();
                var Db = 0.0;
                //int i;
                AGA10_Init();

                A10.lStatus = 9000;
                A10.Methane = ParseDoubleOrDefault(result[streamNo + ":C1.."]) / 100;
                A10.Nitrogen = ParseDoubleOrDefault(result[streamNo + ":N2.."]) / 100;
                A10.CO2 = ParseDoubleOrDefault(result[streamNo + ":CO2.."]) / 100;
                A10.Ethane = ParseDoubleOrDefault(result[streamNo + ":C2.."]) / 100;
                A10.Propane = ParseDoubleOrDefault(result[streamNo + ":C3.."]) / 100;
                A10.H2O = 0.0;
                A10.H2S = 0.0;
                A10.H2 = 0.0;
                A10.CO = 0.0;
                A10.O2 = 0.0;
                A10.i_Butane = ParseDoubleOrDefault(result[streamNo + ":iC4.."]) / 100;
                A10.n_Butane = ParseDoubleOrDefault(result[streamNo + ":nC4.."]) / 100;
                var moleNeoC5 = ParseDoubleOrDefault(result[streamNo + ":neoC5.."]) / 100;
                var moleiC5 = ParseDoubleOrDefault(result[streamNo + ":iC5.."]) / 100;
                A10.i_Pentane = moleiC5 + moleNeoC5; //add neoC5 to iC5
                A10.n_Pentane = ParseDoubleOrDefault(result[streamNo + ":nC5.."]) / 100;
                A10.n_Hexane = ParseDoubleOrDefault(result[streamNo + ":C6.."]) / 100;
                A10.n_Heptane = 0.0;
                A10.n_Octane = 0.0;
                A10.n_Nonane = 0.0;
                A10.n_Decane = 0.0;
                A10.He = 0.0;
                A10.Ar = 0.0;

                A10.dPb = 101.325 * 1000; //pa
                A10.dPf = ParseDoubleOrDefault(result[streamNo + ":Pf.."]) * 1000; //pa
                A10.dTb = 20.0 + 273.15;
                A10.dTf = ParseDoubleOrDefault(result[streamNo + ":Tf.."]) + 273.15;

                var totalComposition =
                    A10.Methane
                    + A10.Ethane
                    + A10.Propane
                    + A10.i_Butane
                    + A10.n_Butane
                    + A10.i_Pentane
                    + A10.n_Pentane
                    + A10.n_Hexane
                    + A10.n_Heptane
                    + A10.n_Octane
                    + A10.n_Nonane
                    + A10.n_Decane
                    + A10.He
                    + A10.Ar;
                if (Math.Abs(totalComposition - 100.0) > 0.2)
                {
                    Log.Error(
                        "SOSCalculate_Auto("
                        + dateTime
                        + ","
                        + streamNo
                        + ","
                        + oeTableName
                        + "):"
                        + "The total composition isn't 100！"
                    );
                    return -2;
                }

                if ((Math.Round(Tb) != 20) | (Math.Round(Pb, 3) != 101.325))
                {
                    Log.Error(
                        "SOSCalculate_Auto("
                        + dateTime
                        + ","
                        + streamNo
                        + ","
                        + oeTableName
                        + "):"
                        + "Pb or Tb ERROR！"
                    );
                    return -3;
                }

                Crit(ref A10, Db);

                calSOS = A10.dSOS;
                Df = A10.dRhof;
                Ds = A10.dRhob;
                //Text28.Text = string.Format("{0:F8}", A10.dRD_Ideal);
                //Text29.Text = string.Format("{0:F8}", A10.dRD_Real);
                //txtCalZb.Text = string.Format("{0:F8}", A10.dZb);
                //txtCalZf.Text = string.Format("{0:F8}", A10.dZf);

                AGA10_UnInit();

                var devSOS = Math.Round((AvgSndVel - calSOS) / calSOS, 5);
                return 0;
            }
            catch (Exception ex)
            {
                Log.Error(
                    "SOSCalculate_Auto("
                    + dateTime
                    + ","
                    + streamNo
                    + ","
                    + oeTableName
                    + "):"
                    + ex.Message
                );
                return -100;
            }
        }

        private async void cbStream_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 检查是否选择了默认项
            if (cbFlowMeter.SelectedIndex <= 0)
            {
                // 清空并重置控件
                ResetFlowComputerControls();
                return;
            }

            try
            {
                // 获取选中的stream
                string selectedStream = cbFlowMeter.SelectedItem.ToString();

                // 禁用相关控件
                cbFlowMeter.Enabled = false;
                cbFlowComputer.Enabled = false;
                tbCurrentStreamNo.Enabled = false;

                // 显示加载提示
                cbFlowComputer.Items.Clear();
                cbFlowComputer.Items.Add("Loading...");
                cbFlowComputer.SelectedIndex = 0;


                // 获取FlowComputerTag和Stream映射数据
                var tagStreamPairs = await _stationManager.GetFlowComputerTagAndStreamAsync(selectedStream);

                // 清空并重新填充映射字典
                _tagStreamMapping.Clear();
                foreach (var pair in tagStreamPairs)
                {
                    _tagStreamMapping[pair.FlowComputerTag] = pair.Stream;
                }

                // 更新cbFlowComputer的选项
                cbFlowComputer.Items.Clear();
                if (_tagStreamMapping.Count > 0)
                {
                    // 添加所有FlowComputerTags
                    cbFlowComputer.Items.AddRange(_tagStreamMapping.Keys.ToArray());

                    // 直接选择第一项，这将触发SelectedIndexChanged事件
                    cbFlowComputer.SelectedIndex = 0;
                    cbFlowComputer.Enabled = true;
                    tbCurrentStreamNo.Enabled = true;
                    // 获取第一个FlowComputerTag对应的Stream值
                    string firstTag = cbFlowComputer.Items[0].ToString();
                    if (_tagStreamMapping.TryGetValue(firstTag, out string streamValue))
                    {
                        tbCurrentStreamNo.Text = streamValue;
                    }
                }
                else
                {
                    cbFlowComputer.Items.Add("-- No Data --");
                    cbFlowComputer.SelectedIndex = 0;
                    cbFlowComputer.Enabled = false;
                    tbCurrentStreamNo.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Loading FC list FAILED：{ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                ResetFlowComputerControls();
            }
            finally
            {
                cbFlowMeter.Enabled = true;
            }
        }


        private void cbFlowComputer_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string selectedTag = cbFlowComputer.SelectedItem.ToString();

                // 从映射中获取对应的Stream值
                if (_tagStreamMapping.TryGetValue(selectedTag, out string streamValue))
                {
                    tbCurrentStreamNo.Text = streamValue;

                    historyTagSuffix = "_" + tbCurrentStreamNo.Text;
                    fieldTypeStatuses.Clear();
                    fieldTypeStatuses.Add("SLCT", new FieldTypeStatus());
                    fieldTypeStatuses.Add("PressInuse", new FieldTypeStatus());
                    fieldTypeStatuses.Add("TempInuse", new FieldTypeStatus());
                    fieldTypeStatuses.Add("USMAvgVOS", new FieldTypeStatus());

                    InitializeFieldPanels(historyTagSuffix);
                }
                else
                {
                    tbCurrentStreamNo.Text = string.Empty;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Setting Stream List FAILED：{ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                tbCurrentStreamNo.Text = string.Empty;
            }
        }

        // 辅助方法：重置FlowComputer相关控件
        private void ResetFlowComputerControls()
        {
            _tagStreamMapping.Clear();

            cbFlowComputer.Items.Clear();
            cbFlowComputer.Items.Add("-- Select FC --");
            cbFlowComputer.SelectedIndex = 0;
            cbFlowComputer.Enabled = false;

            tbCurrentStreamNo.Text = string.Empty;
            tbCurrentStreamNo.Enabled = false;
        }

        public struct AGA10STRUCT
        {
            public int lStatus; // /* calculation status */
            public int bForceUpdate; // /* signal To perform full calculation */
            public double Methane;
            public double Nitrogen;
            public double CO2;
            public double Ethane;
            public double Propane;
            public double H2O;
            public double H2S;
            public double H2;
            public double CO;
            public double O2;
            public double i_Butane;
            public double n_Butane;
            public double i_Pentane;
            public double n_Pentane;
            public double n_Hexane;
            public double n_Heptane;
            public double n_Octane;
            public double n_Nonane;
            public double n_Decane;
            public double He;
            public double Ar;
            public double dPb; //* Contract base Pressure (Pa) */
            public double dTb; //* Contract base temperature (K) */
            public double dPf; //* Absolute Pressure (Pa) */
            public double dTf; //* Flowing temperature (K) */
            public double dMrx; //* mixture molar mass */
            public double dZb; //* compressibility at contract base condition */
            public double dZf; //* compressibility at flowing condition */
            public double dFpv; //* supercompressibility */
            public double dDb; //* molar density at contract base conditions (moles/dm3) */
            public double dDf; //* molar density at flowing conditions (moles/dm3) */
            public double dRhob; //* mass density at contract base conditions (kg/m3) */
            public double dRhof; //* mass density at flowing conditions (kg/m3) */
            public double dRD_Ideal; //* ideal gas relative density */
            public double dRD_Real; //* real gas relative density */
            public double dHo; //* ideal gas specific enthalpy */
            public double dH; //* real gas specific enthalpy (J/kg) */
            public double dS; //* real gas specific entropy (J/kg-mol.K)*/
            public double dCpi; //* ideal gas constant pressure heat capacity (J/kg-mol.K)*/
            public double dCp; //* real gas constant pressure heat capacity (J/kg-mol.K)*/
            public double dCv; //* real gas constant volume heat capacity (J/kg-mol.K)*/
            public double dk; //* ratio of specific heats */
            public double dKappa; //* isentropic exponent, denoted with Greek letter kappa */
            public double dSOS; //* speed of sound (m/s) */
            public double dCstar; //* critical flow factor C* */
        }

        private void Poller_NewDataReceived(object sender, Dictionary<string, object> e)
        {
            this.BeginInvoke((MethodInvoker)delegate { UpdateUI(e); });
        }

        private void Poller_ErrorOccurred(object sender, Exception e)
        {
            Log.Error($"An error occurred: {e.Message}");
        }

        private void UpdateUI(Dictionary<string, object> data)
        {
            foreach (var item in data)
            {
                if (item.Key.Contains("StatusA"))
                {
                    // 尝试将值转换为数字
                    if (double.TryParse(item.Value.ToString(), out double numericValue))
                    {
                        // 如果值为0，设置面板颜色为绿色，否则为红色
                        panelStatusA.BackColor = numericValue == 0 ? Color.Green : Color.Yellow;
                    }
                    else
                    {
                        // 如果无法转换为数字，将面板设置为默认颜色
                        panelStatusA.BackColor = SystemColors.Control;
                    }
                }

                if (item.Key.Contains("StatusB"))
                {
                    // 尝试将值转换为数字
                    if (double.TryParse(item.Value.ToString(), out double numericValue))
                    {
                        // 如果值为0，设置面板颜色为绿色，否则为红色
                        panelStatusB.BackColor = numericValue == 0 ? Color.Green : Color.Yellow;
                    }
                    else
                    {
                        // 如果无法转换为数字，将面板设置为默认颜色
                        panelStatusB.BackColor = SystemColors.Control;
                    }
                }

                if (item.Key.Contains("StatusC"))
                {
                    // 尝试将值转换为数字
                    if (double.TryParse(item.Value.ToString(), out double numericValue))
                    {
                        // 如果值为0，设置面板颜色为绿色，否则为红色
                        panelStatusC.BackColor = numericValue == 0 ? Color.Green : Color.Yellow;
                    }
                    else
                    {
                        // 如果无法转换为数字，将面板设置为默认颜色
                        panelStatusC.BackColor = SystemColors.Control;
                    }
                }

                if (item.Key.Contains("StatusD"))
                {
                    // 尝试将值转换为数字
                    if (double.TryParse(item.Value.ToString(), out double numericValue))
                    {
                        // 如果值为0，设置面板颜色为绿色，否则为红色
                        panelStatusD.BackColor = numericValue == 0 ? Color.Green : Color.Yellow;
                    }
                    else
                    {
                        // 如果无法转换为数字，将面板设置为默认颜色
                        panelStatusD.BackColor = SystemColors.Control;
                    }
                }

                if (item.Key.Contains("SystemStatus"))
                {
                    // 尝试将值转换为数字
                    if (double.TryParse(item.Value.ToString(), out double numericValue))
                    {
                        // 如果值为0，设置面板颜色为绿色，否则为红色
                        panelSystemStatus.BackColor = numericValue == 0 ? Color.Green : Color.Yellow;
                    }
                    else
                    {
                        // 如果无法转换为数字，将面板设置为默认颜色
                        panelSystemStatus.BackColor = SystemColors.Control;
                    }
                }
            }
        }


        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (poller != null)
            {
                poller.Stop();
                poller.Dispose();
            }

            base.OnFormClosing(e);
        }

        private void btnOpenReportFolder_Click(object sender, EventArgs e)
        {
            var savedPath = _reportFolderPath + cbStation.Text.Trim() + @"\" + cbFlowMeter.Text.Trim() +
                            @"\SOSCheckReport\";
            OpenFolder(savedPath);
        }

        static void OpenFolder(string folderPath)
        {
            try
            {
                // 检查文件夹是否存在
                if (System.IO.Directory.Exists(folderPath))
                {
                    // 使用 Process.Start 打开文件夹
                    Process.Start("explorer.exe", folderPath);
                }
                else
                {
                    MessageBox.Show("Please select the stream first.");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Open folder error: {ex.Message}");
            }
        }

        private double ParseDoubleOrDefault(string value, double defaultValue = 0)
        {
            if (double.TryParse(value, out double result))
            {
                return Math.Round(result, 6);
            }

            return defaultValue;
        }

        private async Task LoadStationNameData()
        {
            try
            {
                // 在加载数据之前禁用ComboBox
                cbStation.Enabled = false;

                // 显示加载提示
                cbStation.Items.Clear();
                cbStation.Items.Add("Loading...");
                cbStation.SelectedIndex = 0;
                // 异步获取站点列表
                List<string> stations = await _stationManager.GetAllStationsAsync();
                // 更新ComboBox
                cbStation.Items.Clear();

                // 可以添加一个默认选项
                cbStation.Items.Add("-- Select Station --");

                // 添加所有站点
                cbStation.Items.AddRange(stations.ToArray());
                // 选择默认项
                cbStation.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Loading Station List FAILED：{ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 发生错误时清空并添加提示
                cbStation.Items.Clear();
                cbStation.Items.Add("Loading FAILED");
                cbStation.SelectedIndex = 0;
            }
            finally
            {
                // 重新启用ComboBox
                cbStation.Enabled = true;
            }
        }

        private async void cbStation_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 检查是否选择了默认项
            if (cbStation.SelectedIndex <= 0)
            {
                // 清空stream下拉框并添加默认项
                cbFlowMeter.Items.Clear();
                cbFlowMeter.Items.Add("-- Select Station --");
                cbFlowMeter.SelectedIndex = 0;
                cbFlowMeter.Enabled = false;
                return;
            }

            try
            {
                // 获取选中的station
                string selectedStation = cbStation.SelectedItem.ToString();

                // 禁用两个下拉框，防止加载过程中用户操作
                cbStation.Enabled = false;
                cbFlowMeter.Enabled = false;

                // 显示加载提示
                cbFlowMeter.Items.Clear();
                cbFlowMeter.Items.Add("Loading...");
                cbFlowMeter.SelectedIndex = 0;

                // 异步获取FlowMeterTags
                List<string> flowMeterTags = await _stationManager.GetFlowMeterTagsByStationAsync(selectedStation);

                // 更新cbStream的选项
                cbFlowMeter.Items.Clear();

                // 添加默认选项
                cbFlowMeter.Items.Add("-- Select Meter --");

                if (flowMeterTags.Count > 0)
                {
                    // 添加所有FlowMeterTags
                    cbFlowMeter.Items.AddRange(flowMeterTags.ToArray());
                    cbFlowMeter.SelectedIndex = 0;
                    cbFlowMeter.Enabled = true;
                }
                else
                {
                    // 如果没有数据，添加提示信息
                    cbFlowMeter.Items.Clear();
                    cbFlowMeter.Items.Add("-- No Meter --");
                    cbFlowMeter.SelectedIndex = 0;
                    cbFlowMeter.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Loading Meter List FAILED：{ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 发生错误时显示错误提示
                cbFlowMeter.Items.Clear();
                cbFlowMeter.Items.Add("Loading FAILED");
                cbFlowMeter.SelectedIndex = 0;
                cbFlowMeter.Enabled = false;
            }
            finally
            {
                // 重新启用station下拉框
                cbStation.Enabled = true;
            }
        }

        private void InitializeFieldMappings()
        {
            fieldMappings.Clear();

            if (isRMGDevice)
            {
                // RMG设备字段映射规则 - 只映射实际存在的4个标准字段
                fieldMappings["FlowVelA"] = "P1Velocity";
                fieldMappings["FlowVelB"] = "P2Velocity";
                fieldMappings["FlowVelC"] = "P3Velocity";
                fieldMappings["FlowVelD"] = "P4Velocity";

                // 其他字段映射（根据实际RMG字段结构调整）
                fieldMappings["SndVelA"] = "P1SoundVel";
                fieldMappings["SndVelB"] = "P2SoundVel";
                fieldMappings["SndVelC"] = "P3SoundVel";
                fieldMappings["SndVelD"] = "P4SoundVel";

                fieldMappings["StatusA"] = "P1Fault";
                fieldMappings["StatusB"] = "P2Fault";
                fieldMappings["StatusC"] = "P3Fault";
                fieldMappings["StatusD"] = "P4Fault";

                // 可以根据需要添加更多映射规则，例如：
                // fieldMappings["PctGoodA1"] = "P1PctGood1";
                // fieldMappings["PctGoodA2"] = "P1PctGood2";
                // 等等...
            }
            // 非RMG设备时，fieldMappings为空，使用原始字段名
        }

        /// <summary>
        /// 获取映射后的字段名
        /// </summary>
        /// <param name="originalFieldName">原始字段名</param>
        /// <returns>映射后的字段名</returns>
        private string GetMappedFieldName(string originalFieldName)
        {
            if (fieldMappings.ContainsKey(originalFieldName))
            {
                return fieldMappings[originalFieldName];
            }

            return originalFieldName; // 未映射的字段保持原名
        }

        /// <summary>
        /// 安全的字段访问方法 - 支持字段映射和向后兼容
        /// </summary>
        /// <param name="dataRow">数据行</param>
        /// <param name="streamTablePrefix">表前缀</param>
        /// <param name="fieldName">字段名</param>
        /// <returns>字段值</returns>
        private double GetMappedFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix,
            string fieldName)
        {
            // 获取映射后的字段名
            string mappedFieldName = GetMappedFieldName(fieldName);
            string fullFieldName = streamTablePrefix + mappedFieldName;

            // 尝试访问映射后的字段
            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }

            // 向后兼容：如果映射字段不存在，尝试原字段名
            string originalFullFieldName = streamTablePrefix + fieldName;
            if (dataRow.ContainsKey(originalFullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[originalFullFieldName]);
            }

            // 都不存在时记录警告并返回默认值
            Log.Error($"字段不存在: {fullFieldName} 或 {originalFullFieldName}");
            return 0.0;
        }

        /// <summary>
        /// RMG特有字段直接访问方法
        /// </summary>
        /// <param name="dataRow">数据行</param>
        /// <param name="streamTablePrefix">表前缀</param>
        /// <param name="rmgFieldName">RMG字段名（如P5Velocity）</param>
        /// <returns>字段值</returns>
        private double GetRMGSpecificFieldValue(Dictionary<string, string> dataRow, string streamTablePrefix,
            string rmgFieldName)
        {
            string fullFieldName = streamTablePrefix + rmgFieldName;

            if (dataRow.ContainsKey(fullFieldName))
            {
                return ParseDoubleOrDefault(dataRow[fullFieldName]);
            }

            Log.Error($"RMG特有字段不存在: {fullFieldName}");
            return 0.0;
        }

        private double GetMappedFieldValueSimple(Dictionary<string, string> dataDict, string fieldName)
        {
            // 获取映射后的字段名
            string mappedFieldName = GetMappedFieldName(fieldName);

            // 尝试访问映射后的字段
            if (dataDict.ContainsKey(mappedFieldName))
            {
                return ParseDoubleOrDefault(dataDict[mappedFieldName]);
            }

            // 向后兼容：如果映射字段不存在，尝试原字段名
            if (dataDict.ContainsKey(fieldName))
            {
                return ParseDoubleOrDefault(dataDict[fieldName]);
            }

            Log.Error($"字段不存在: {mappedFieldName} 或 {fieldName}");
            return 0.0;
        }

        /// <summary>
        /// 【简化版-RMG特有】直接访问RMG特有字段
        /// </summary>
        private double GetRMGSpecificFieldValueSimple(Dictionary<string, string> dataDict, string rmgFieldName)
        {
            if (dataDict.ContainsKey(rmgFieldName))
            {
                return ParseDoubleOrDefault(dataDict[rmgFieldName]);
            }

            Log.Error($"RMG特有字段不存在: {rmgFieldName}");
            return 0.0;
        }

        /// <summary>
        /// 新增功能：插入CheckList记录
        /// 根据流量计算机标签和流量计标签查询Selection_Table，并将结果连同VOS参数插入到CheckList_Table
        /// </summary>
        /// <param name="streamFcTableName">流量计算机标签</param>
        /// <param name="streamFtTableName">流量计标签</param>
        /// <param name="flowMeterCalVOS">流量计计算声速</param>
        /// <param name="flowMeterUSMAvgVOS">流量计超声波平均声速</param>
        /// <param name="flowMeterDeviationVOS">流量计声速偏差</param>
        /// <param name="flowMeterCheckTime">流量计检查时间</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> InsertCheckListRecordAsync(
            string streamFcTableName,
            string streamFtTableName,
            string flowMeterCalVOS,
            string flowMeterUSMAvgVOS,
            string flowMeterDeviationVOS,
            DateTime flowMeterCheckTime)
        {
            try
            {
                Log.Info($"Form1调用InsertCheckListRecord - FC: {streamFcTableName}, FT: {streamFtTableName}");

                var result = await checkListDataService.InsertCheckListRecordAsync(
                    streamFcTableName,
                    streamFtTableName,
                    flowMeterCalVOS,
                    flowMeterUSMAvgVOS,
                    flowMeterDeviationVOS,
                    flowMeterCheckTime);

                if (result)
                {
                    Log.Info($"CheckList记录插入成功 - FC: {streamFcTableName}, FT: {streamFtTableName}");
                }
                else
                {
                    Log.Warn($"CheckList记录插入失败 - FC: {streamFcTableName}, FT: {streamFtTableName}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"Form1调用InsertCheckListRecord时发生异常 - FC: {streamFcTableName}, FT: {streamFtTableName}", ex);
                LogHelper.LogError(ex, $"Form1调用InsertCheckListRecord失败，FC: {streamFcTableName}, FT: {streamFtTableName}");
                return false;
            }
        }

        /// <summary>
        /// 示例方法：演示如何使用InsertCheckListRecordAsync方法
        /// 这个方法可以在需要的地方调用，比如在数据检查完成后
        /// </summary>
        private async void ExampleUsageOfInsertCheckListRecord()
        {
            try
            {
                // 示例参数 - 实际使用时应该从界面或计算结果中获取
                string fcTag = cbFlowComputer?.Text ?? ""; // 从界面获取流量计算机标签
                string ftTag = cbFlowMeter?.Text ?? "";    // 从界面获取流量计标签
                string calVOS = txtCalSOS?.Text ?? "";     // 从界面获取计算声速
                string usmAvgVOS = txtFCSOS?.Text ?? "";   // 从界面获取超声波平均声速
                string deviationVOS = txtDevSOS?.Text ?? ""; // 从界面获取声速偏差
                DateTime checkTime = DateTime.Now;        // 检查时间，可以是当前时间或指定时间

                // 调用插入方法
                bool success = await InsertCheckListRecordAsync(fcTag, ftTag, calVOS, usmAvgVOS, deviationVOS, checkTime);

                if (success)
                {
                    // 成功时的处理
                    lbMessage.ForeColor = Color.Green;
                    lbMessage.Text = "CheckList记录插入成功！";
                }
                else
                {
                    // 失败时的处理
                    lbMessage.ForeColor = Color.Red;
                    lbMessage.Text = "CheckList记录插入失败！";
                }
            }
            catch (Exception ex)
            {
                Log.Error("示例方法执行异常", ex);
                lbMessage.ForeColor = Color.Red;
                lbMessage.Text = "操作异常，请查看日志！";
            }
        }
    }
}