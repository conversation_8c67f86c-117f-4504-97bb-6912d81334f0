﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlowCheck_DPLNG
{
    public class StationDataManager
    {
        private readonly string _connectionString;
        private readonly string _tableName;

        public StationDataManager(string connectionString, string tableName)
        {
            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentNullException(nameof(connectionString));
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentNullException(nameof(tableName));

            _connectionString = connectionString;
            _tableName = tableName;
        }

        public async Task<List<string>> GetAllStationsAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand($"SELECT DISTINCT Station FROM {_tableName}", connection))
                    {
                        List<string> stations = new List<string>();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string station = reader.GetString(0);
                                if (!string.IsNullOrEmpty(station))
                                {
                                    stations.Add(station);
                                }
                            }
                        }

                        return stations;
                    }
                }
            }
            catch (SqlException ex)
            {
                throw new Exception($"数据库查询失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取站点列表时发生错误: {ex.Message}", ex);
            }
        }

        public async Task<List<string>> GetFlowMeterTagsByStationAsync(string stationName)
        {
            if (string.IsNullOrEmpty(stationName))
                throw new ArgumentNullException(nameof(stationName));

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command =
                           new SqlCommand(
                               $"SELECT DISTINCT FlowMeterTag FROM {_tableName} WHERE Station = @StationName",
                               connection))
                    {
                        command.Parameters.AddWithValue("@StationName", stationName);

                        List<string> flowMeterTags = new List<string>();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string flowMeterTag = reader.GetString(0);
                                if (!string.IsNullOrEmpty(flowMeterTag))
                                {
                                    flowMeterTags.Add(flowMeterTag);
                                }
                            }
                        }

                        return flowMeterTags;
                    }
                }
            }
            catch (SqlException ex)
            {
                throw new Exception($"数据库查询失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取流量计标签列表时发生错误: {ex.Message}", ex);
            }
        }

        public async Task<List<(string FlowComputerTag, string Stream)>> GetFlowComputerTagAndStreamAsync(
            string flowMeterTag)
        {
            if (string.IsNullOrEmpty(flowMeterTag))
                throw new ArgumentNullException(nameof(flowMeterTag));

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(
                               $"SELECT DISTINCT FlowComputerTag, Stream FROM {_tableName} WHERE FlowMeterTag = @FlowMeterTag",
                               connection))
                    {
                        command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);

                        var results = new List<(string FlowComputerTag, string Stream)>();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                string flowComputerTag = reader.GetString(0);
                                string stream = reader.GetString(1);
                                if (!string.IsNullOrEmpty(flowComputerTag) && !string.IsNullOrEmpty(stream))
                                {
                                    results.Add((flowComputerTag, stream));
                                }
                            }
                        }

                        return results;
                    }
                }
            }
            catch (SqlException ex)
            {
                throw new Exception($"数据库查询失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取流量计算机标签和流信息时发生错误: {ex.Message}", ex);
            }
        }

        public async Task<string> GetDeviceManufacturerAsync(string flowMeterTag)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(
                           $"SELECT DeviceManufacturer FROM {_tableName} WHERE FlowMeterTag = @FlowMeterTag",
                           connection))
                {
                    command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                    var result = await command.ExecuteScalarAsync();
                    return result?.ToString() ?? "未知厂商";
                }
            }
        }
    }
}