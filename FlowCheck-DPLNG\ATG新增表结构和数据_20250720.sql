/****** Object:  Table [dbo].[CheckList_Table]    Script Date: 2025/7/20 9:34:48 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CheckList_Table](
	[id] [float] NOT NULL,
	[Station] [nvarchar](255) NULL,
	[FlowComputerTag] [nvarchar](255) NULL,
	[Stream] [nvarchar](255) NULL,
	[FlowMeterTag] [nvarchar](255) NULL,
	[StationTag] [varchar](50) NULL,
	[DeviceManufacturer] [varchar](50) NULL,
	[FlowMeterSerialNumber] [nvarchar](50) NULL,
	[FlowMeterCalVOS] [nvarchar](50) NULL,
	[FlowMeterUSMAvgVOS] [nvarchar](50) NULL,
	[FlowMeterDeviationVOS] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Selection_Table]    Script Date: 2025/7/20 9:34:48 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Selection_Table](
	[id] [float] NOT NULL,
	[Station] [nvarchar](255) NULL,
	[FlowComputerTag] [nvarchar](255) NULL,
	[Stream] [nvarchar](255) NULL,
	[FlowMeterTag] [nvarchar](255) NULL,
	[StationTag] [varchar](50) NULL,
	[DeviceManufacturer] [varchar](50) NULL,
	[FlowMeterSerialNumber] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (1, N'WKC1', N'WKC1_FQIC_5101', N'1', N'WKC1_FT_5101', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (2, N'WKC1', N'WKC1_FQIC_5201', N'1', N'WKC1_FT_5201', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (3, N'WKC1', N'WKC1_FQIC_5301', N'1', N'WKC1_FT_5301', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (4, N'WKC1', N'WKC1_FQIC_5401', N'1', N'WKC1_FT_5401', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (5, N'WKC1', N'WKC1_FQIC_5501', N'1', N'WKC1_FT_5501', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (6, N'WKC1', N'WKC1_FQIC_5601', N'1', N'WKC1_FT_5601', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (7, N'WKC1', N'WKC1_FQIC_5701', N'1', N'WKC1_FT_5701', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (8, N'WKC1', N'WKC1_FQIC_5801', N'1', N'WKC1_FT_5801', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (9, N'WKC1', N'WKC1_FQIC_5901', N'1', N'WKC1_FT_5901', N'WKC1', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (11, N'MS', N'MS_FQIC_5101', N'1', N'MS_FT_5101', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (12, N'MS', N'MS_FQIC_5201', N'1', N'MS_FT_5201', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (13, N'MS', N'MS_FQIC_5301', N'1', N'MS_FT_5301', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (14, N'MS', N'MS_FQIC_5401', N'1', N'MS_FT_5401', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (15, N'MS', N'MS_FQIC_5501', N'1', N'MS_FT_5501', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (16, N'MS', N'MS_FQIC_5601', N'1', N'MS_FT_5601', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (17, N'MS', N'MS_FQIC_5701', N'1', N'MS_FT_5701', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (18, N'MS', N'MS_FQIC_5801', N'1', N'MS_FT_5801', N'MS', N'DANIEL', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (21, N'UCS1', N'UCS1_FQIC_150101', N'1', N'UCS1_FT_150101', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (22, N'UCS1', N'UCS1_FQIC_150201', N'1', N'UCS1_FT_150201', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (23, N'UCS1', N'UCS1_FQIC_150301', N'1', N'UCS1_FT_150301', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (24, N'UCS1', N'UCS1_FQIC_150401', N'1', N'UCS1_FT_150401', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (25, N'UCS1', N'UCS1_FQIC_150501', N'1', N'UCS1_FT_150501', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (26, N'UCS1', N'UCS1_FQIC_150601', N'1', N'UCS1_FT_150601', N'UCS1', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (31, N'UKMS', N'UKMS_FQIC_150101', N'1', N'UKMS_FT_150101', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (32, N'UKMS', N'UKMS_FQIC_150201', N'1', N'UKMS_FT_150201', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (33, N'UKMS', N'UKMS_FQIC_150301', N'1', N'UKMS_FT_150301', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (34, N'UKMS', N'UKMS_FQIC_150401', N'1', N'UKMS_FT_150401', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (35, N'UKMS', N'UKMS_FQIC_150501', N'1', N'UKMS_FT_150501', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (36, N'UKMS', N'UKMS_FQIC_150601', N'1', N'UKMS_FT_150601', N'UKMS', N'RMG', NULL)
GO
INSERT [dbo].[Selection_Table] ([id], [Station], [FlowComputerTag], [Stream], [FlowMeterTag], [StationTag], [DeviceManufacturer], [FlowMeterSerialNumber]) VALUES (37, N'UKMS', N'UKMS_FQIC_150701', N'1', N'UKMS_FT_150701', N'UKMS', N'RMG', NULL)
GO
